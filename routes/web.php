<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Route;

Route::get('/2fa', [\App\Http\Controllers\Auth\TwoFactorController::class, 'show'])->name('2fa');
Route::post('/2fa', [\App\Http\Controllers\Auth\TwoFactorController::class, 'verify'])->name('2fa.verify');
Route::post('/2fa/activate', [\App\Http\Controllers\Auth\TwoFactorController::class, 'active'])->name('2fa.activate');

Route::post('subscriber-form', [\App\Http\Controllers\HomeController::class, 'SubscriberForm']);

Route::get('/api/countries', function () {
    return response()->json(\App\Country::all());
});

// Wallet test route
Route::get('/wallet-test', function () {
    return 'Wallet package is working!';
});
