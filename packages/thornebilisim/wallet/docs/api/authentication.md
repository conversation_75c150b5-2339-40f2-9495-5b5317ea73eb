# Kimlik Doğrulama

Wallet API'sine erişim için müşteri kimlik doğrulaması gereklidir.

## Authentication Middleware

API endpoint'leri `customer` middleware'i kullanır. Bu middleware, isteklerin kimlik doğrulaması yapılmış müşterilerden geldiğini doğrular.

## Header Gereksinimleri

Kimlik doğrulama gerektiren endpoint'ler için aşağıdaki header'lar gereklidir:

```http
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json
```

## Kimlik Doğrulama Hatası

Kimlik doğrulama başarısız olduğunda aşağıdaki response döner:

```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

**HTTP Status**: `401 Unauthorized`

## Public Endpoint'ler

Aşağıdaki endpoint'ler kimlik doğrulama gerektirmez:

- `GET /api/wallet/currencies` - Desteklenen para birimleri
- `GET /api/wallet/payment-methods` - <PERSON><PERSON><PERSON>
- `POST /api/wallet/validate-account-number` - Hesap numarası doğrulama

## Authenticated Endpoint'ler

Diğer tüm endpoint'ler kimlik doğrulama gerektirir ve müşterinin kendi verilerine erişim sağlar.

## Güvenlik Notları

- Token'lar güvenli bir şekilde saklanmalıdır
- HTTPS kullanımı zorunludur
- Token'lar düzenli olarak yenilenmelidir
- API istekleri rate limiting'e tabidir
