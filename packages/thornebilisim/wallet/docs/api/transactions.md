# İşlem Geçmişi

Müşterinin tüm cüzdan işlemlerini görüntülemek için kullan<PERSON>lan endpoint'ler.

## İşlem Listeleme

Müşterinin tüm cüzdan işlemlerini (para yatırma, çekme, transfer) listeler.

**Endpoint**: `GET /api/wallet/transactions`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `type` | string | Hayır | İşlem tipi (deposit, withdrawal, transfer_sent, transfer_received) |
| `currency` | string | Hayır | Para birimi filtresi (EUR, USD, MLGR) |
| `status` | string | Hayır | Durum filtresi (pending, completed, failed, cancelled) |
| `method` | string | Hayır | Ödeme yöntemi filtresi |
| `from_date` | string | Hayır | Ba<PERSON><PERSON><PERSON><PERSON> tarihi (YYYY-MM-DD) |
| `to_date` | string | Hayır | <PERSON><PERSON><PERSON> tarihi (YYYY-MM-DD) |
| `min_amount` | number | Hayır | Minimum miktar filtresi |
| `max_amount` | number | Hayır | Maksimum miktar filtresi |
| `per_page` | integer | Hayır | Sayfa başına kayıt (varsayılan: 15, maksimum: 100) |
| `sort_by` | string | Hayır | Sıralama alanı (created_at, amount) - varsayılan: created_at |
| `sort_order` | string | Hayır | Sıralama yönü (asc, desc) - varsayılan: desc |

### Örnek İstek

```http
GET /api/wallet/transactions?type=deposit&currency=EUR&status=completed&per_page=20&sort_by=amount&sort_order=desc
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "transaction_id": "TXN_DEP_001",
            "type": "deposit",
            "method": "bank_transfer",
            "currency": "EUR",
            "amount": "500.00",
            "status": "completed",
            "description": "Hesaba para yatırma",
            "created_at": "2024-01-15T10:00:00Z",
            "completed_at": "2024-01-15T10:30:00Z",
            "formatted_amount": "€500.00"
        },
        {
            "id": 2,
            "transaction_id": "TXN_WTH_001",
            "type": "withdrawal",
            "method": "bank_transfer",
            "currency": "EUR",
            "amount": "200.00",
            "status": "completed",
            "description": "Hesaptan para çekme",
            "created_at": "2024-01-14T15:00:00Z",
            "completed_at": "2024-01-14T17:00:00Z",
            "formatted_amount": "€200.00"
        },
        {
            "id": 3,
            "transaction_id": "TRF_001",
            "type": "transfer_sent",
            "currency": "EUR",
            "amount": "100.00",
            "status": "completed",
            "description": "Arkadaşa para gönderimi",
            "recipient_account": "EUR9876543",
            "recipient_name": "Jane Doe",
            "created_at": "2024-01-13T12:00:00Z",
            "completed_at": "2024-01-13T12:01:00Z",
            "formatted_amount": "€100.00"
        },
        {
            "id": 4,
            "transaction_id": "TRF_002",
            "type": "transfer_received",
            "currency": "EUR",
            "amount": "75.00",
            "status": "completed",
            "description": "Borç ödeme",
            "sender_account": "EUR5555555",
            "sender_name": "Bob Smith",
            "created_at": "2024-01-12T09:30:00Z",
            "completed_at": "2024-01-12T09:31:00Z",
            "formatted_amount": "€75.00"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 8,
        "per_page": 20,
        "total": 156,
        "from": 1,
        "to": 20
    },
    "summary": {
        "total_transactions": 156,
        "total_deposits": "2500.00",
        "total_withdrawals": "800.00",
        "total_transfers_sent": "450.00",
        "total_transfers_received": "325.00",
        "net_balance_change": "1575.00"
    }
}
```

---

## İşlem Detayı

Belirli bir işlemin detaylarını getirir.

**Endpoint**: `GET /api/wallet/transactions/{transaction_id}`  
**Authentication**: Gerekli

### Örnek İstek

```http
GET /api/wallet/transactions/1
Authorization: Bearer {token}
```

### Para Yatırma Detayı

```json
{
    "success": true,
    "data": {
        "id": 1,
        "transaction_id": "TXN_DEP_001",
        "type": "deposit",
        "method": "bank_transfer",
        "currency": "EUR",
        "amount": "500.00",
        "status": "completed",
        "description": "Hesaba para yatırma",
        "created_at": "2024-01-15T10:00:00Z",
        "completed_at": "2024-01-15T10:30:00Z",
        "formatted_amount": "€500.00",
        "fees": {
            "fixed": "0.00",
            "percentage": "0.00",
            "total": "0.00"
        },
        "metadata": {
            "ip_address": "***********",
            "user_agent": "Mozilla/5.0...",
            "reference_number": "REF123456"
        },
        "company_accounts": [
            {
                "bank_name": "Example Bank",
                "account_holder": "Company Name",
                "iban": "**************************"
            }
        ]
    }
}
```

### Transfer Detayı

```json
{
    "success": true,
    "data": {
        "id": 3,
        "transaction_id": "TRF_001",
        "type": "transfer_sent",
        "currency": "EUR",
        "amount": "100.00",
        "status": "completed",
        "description": "Arkadaşa para gönderimi",
        "from_account_number": "EUR1234567",
        "to_account_number": "EUR9876543",
        "recipient_name": "Jane Doe",
        "created_at": "2024-01-13T12:00:00Z",
        "completed_at": "2024-01-13T12:01:00Z",
        "formatted_amount": "€100.00",
        "fees": {
            "fixed": "0.00",
            "percentage": "0.00",
            "total": "0.00"
        },
        "metadata": {
            "ip_address": "***********",
            "user_agent": "Mozilla/5.0...",
            "reference_number": "REF789012"
        }
    }
}
```

---

## İşlem Özeti

Belirli bir dönem için işlem özetini getirir.

**Endpoint**: `GET /api/wallet/transactions/summary`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `period` | string | Hayır | Dönem (today, week, month, year, custom) - varsayılan: month |
| `from_date` | string | Hayır | Başlangıç tarihi (period=custom için gerekli) |
| `to_date` | string | Hayır | Bitiş tarihi (period=custom için gerekli) |
| `currency` | string | Hayır | Para birimi filtresi |

### Örnek İstek

```http
GET /api/wallet/transactions/summary?period=month&currency=EUR
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "period": "month",
        "from_date": "2024-01-01",
        "to_date": "2024-01-31",
        "currency": "EUR",
        "summary": {
            "total_transactions": 25,
            "deposits": {
                "count": 8,
                "total_amount": "2500.00",
                "average_amount": "312.50"
            },
            "withdrawals": {
                "count": 5,
                "total_amount": "800.00",
                "average_amount": "160.00"
            },
            "transfers_sent": {
                "count": 7,
                "total_amount": "450.00",
                "average_amount": "64.29"
            },
            "transfers_received": {
                "count": 5,
                "total_amount": "325.00",
                "average_amount": "65.00"
            },
            "net_change": "1575.00",
            "fees_paid": "12.00"
        },
        "daily_breakdown": [
            {
                "date": "2024-01-15",
                "deposits": "500.00",
                "withdrawals": "200.00",
                "transfers_sent": "100.00",
                "transfers_received": "0.00",
                "net_change": "200.00"
            }
        ]
    }
}
```

---

## İşlem Arama

İşlemlerde metin bazlı arama yapar.

**Endpoint**: `GET /api/wallet/transactions/search`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `q` | string | Evet | Arama terimi (minimum 3 karakter) |
| `per_page` | integer | Hayır | Sayfa başına kayıt (varsayılan: 15) |

### Örnek İstek

```http
GET /api/wallet/transactions/search?q=arkadaş&per_page=10
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": [
        {
            "id": 3,
            "transaction_id": "TRF_001",
            "type": "transfer_sent",
            "currency": "EUR",
            "amount": "100.00",
            "status": "completed",
            "description": "Arkadaşa para gönderimi",
            "recipient_name": "Jane Doe",
            "created_at": "2024-01-13T12:00:00Z",
            "formatted_amount": "€100.00",
            "highlight": "Arkadaşa para gönderimi"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 1,
        "per_page": 10,
        "total": 1,
        "from": 1,
        "to": 1
    }
}
```

## Hata Durumları

### İşlem Bulunamadı

```json
{
    "success": false,
    "message": "Transaction not found"
}
```

**HTTP Status**: `404 Not Found`

### Geçersiz Tarih Formatı

```json
{
    "success": false,
    "message": "Invalid date format. Use YYYY-MM-DD"
}
```

**HTTP Status**: `400 Bad Request`

### Geçersiz Dönem

```json
{
    "success": false,
    "message": "Invalid period. Use: today, week, month, year, custom"
}
```

**HTTP Status**: `400 Bad Request`

### Arama Terimi Çok Kısa

```json
{
    "success": false,
    "message": "Search query must be at least 3 characters"
}
```

**HTTP Status**: `400 Bad Request`

## İşlem Tipleri

- `deposit` - Para yatırma
- `withdrawal` - Para çekme
- `transfer_sent` - Gönderilen transfer
- `transfer_received` - Alınan transfer

## İşlem Durumları

- `pending` - Beklemede
- `processing` - İşleniyor
- `completed` - Tamamlandı
- `failed` - Başarısız
- `cancelled` - İptal edildi

## Sıralama Seçenekleri

- `created_at` - Oluşturulma tarihi (varsayılan)
- `amount` - Miktar
- `completed_at` - Tamamlanma tarihi

## Filtreleme İpuçları

- Birden fazla filtre aynı anda kullanılabilir
- Tarih filtreleri ISO 8601 formatında olmalıdır
- Miktar filtreleri ondalık sayı kabul eder
- Büyük veri setleri için sayfalama kullanın
