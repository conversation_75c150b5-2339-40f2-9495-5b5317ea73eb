# Yardımcı Endpoint'ler

Sistem bilgileri ve doğrulama işlemleri için kullanılan genel endpoint'ler.

## Desteklenen Para Birimleri (Public)

Sistemde desteklenen para birimlerini listeler.

**Endpoint**: `GET /api/wallet/currencies`  
**Authentication**: Gerekli değil

### Örnek İstek

```http
GET /api/wallet/currencies
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "EUR": {
            "code": "EUR",
            "name": "Euro",
            "symbol": "€",
            "decimal_places": 2,
            "is_default": true,
            "is_enabled": true,
            "exchange_rate": 1.0
        },
        "USD": {
            "code": "USD",
            "name": "US Dollar",
            "symbol": "$",
            "decimal_places": 2,
            "is_default": false,
            "is_enabled": true,
            "exchange_rate": 1.08
        },
        "MLGR": {
            "code": "MLGR",
            "name": "MLGR <PERSON>",
            "symbol": "MLGR",
            "decimal_places": 8,
            "is_default": false,
            "is_enabled": true,
            "exchange_rate": 0.0001,
            "is_crypto": true
        }
    }
}
```

---

## Ödeme Yöntemleri (Public)

Kullanılabilir ödeme yöntemlerini listeler.

**Endpoint**: `GET /api/wallet/payment-methods`  
**Authentication**: Gerekli değil

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `operation` | string | Evet | İşlem tipi (deposit, withdraw) |
| `currency` | string | Hayır | Para birimi filtresi |

### Örnek İstek

```http
GET /api/wallet/payment-methods?operation=deposit&currency=EUR
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "bank_transfer": {
            "key": "bank_transfer",
            "name": "Banka Havalesi",
            "description": "Banka hesabından havale ile para yatırma",
            "enabled": true,
            "can_deposit": true,
            "can_withdraw": true,
            "supported_currencies": ["EUR", "USD"],
            "limits": {
                "min_amount": "10.00",
                "max_amount": "10000.00",
                "daily_limit": "50000.00"
            },
            "fees": {
                "deposit": {
                    "fixed": "0.00",
                    "percentage": "0.00"
                },
                "withdraw": {
                    "fixed": "2.00",
                    "percentage": "0.00"
                }
            },
            "processing_time": {
                "deposit": "1-3 iş günü",
                "withdraw": "1-3 iş günü"
            },
            "required_fields": {
                "deposit": [],
                "withdraw": ["account_holder", "iban", "bank_name"]
            }
        },
        "paypal": {
            "key": "paypal",
            "name": "PayPal",
            "description": "PayPal hesabı ile ödeme",
            "enabled": true,
            "can_deposit": true,
            "can_withdraw": true,
            "supported_currencies": ["EUR", "USD"],
            "limits": {
                "min_amount": "5.00",
                "max_amount": "5000.00",
                "daily_limit": "10000.00"
            },
            "fees": {
                "deposit": {
                    "fixed": "0.30",
                    "percentage": "2.90"
                },
                "withdraw": {
                    "fixed": "0.50",
                    "percentage": "1.50"
                }
            },
            "processing_time": {
                "deposit": "Anında",
                "withdraw": "Anında"
            },
            "required_fields": {
                "deposit": [],
                "withdraw": ["email"]
            }
        }
    }
}
```

---

## Hesap Numarası Doğrulama (Public)

Verilen hesap numarasının geçerli olup olmadığını kontrol eder.

**Endpoint**: `POST /api/wallet/validate-account-number`  
**Authentication**: Gerekli değil

### Request Body

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `account_number` | string | Evet | 11 haneli hesap numarası |

### Örnek İstek

```http
POST /api/wallet/validate-account-number
Content-Type: application/json

{
    "account_number": "EUR1234567"
}
```

### Geçerli Hesap Response

```json
{
    "success": true,
    "message": "Account is valid",
    "data": {
        "is_valid": true,
        "account_number": "EUR1234567",
        "formatted_account_number": "EUR-1234567",
        "currency": "EUR",
        "is_active": true,
        "validation_details": {
            "luhn_check": true,
            "format_check": true,
            "currency_check": true,
            "existence_check": true
        }
    }
}
```

### Geçersiz Hesap Response

```json
{
    "success": false,
    "message": "Invalid account number",
    "data": {
        "is_valid": false,
        "validation_details": {
            "luhn_check": false,
            "format_check": true,
            "currency_check": true,
            "existence_check": false
        },
        "errors": [
            "Luhn algorithm validation failed",
            "Account does not exist"
        ]
    }
}
```

---

## Sistem Durumu

API'nin genel durumunu ve sağlık bilgilerini getirir.

**Endpoint**: `GET /api/wallet/health`  
**Authentication**: Gerekli değil

### Örnek İstek

```http
GET /api/wallet/health
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "status": "healthy",
        "timestamp": "2024-01-15T12:00:00Z",
        "version": "1.0.0",
        "services": {
            "database": {
                "status": "healthy",
                "response_time": "5ms"
            },
            "web3": {
                "status": "healthy",
                "response_time": "120ms"
            },
            "payment_gateway": {
                "status": "healthy",
                "response_time": "80ms"
            }
        },
        "statistics": {
            "total_customers": 1250,
            "total_accounts": 2100,
            "total_transactions_today": 45,
            "system_uptime": "99.9%"
        }
    }
}
```

---

## Döviz Kurları

Güncel döviz kurlarını getirir.

**Endpoint**: `GET /api/wallet/exchange-rates`  
**Authentication**: Gerekli değil

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `base` | string | Hayır | Temel para birimi (varsayılan: EUR) |
| `target` | string | Hayır | Hedef para birimi (belirtilmezse tümü) |

### Örnek İstek

```http
GET /api/wallet/exchange-rates?base=EUR&target=USD
```

### Tek Kur Response

```json
{
    "success": true,
    "data": {
        "base": "EUR",
        "target": "USD",
        "rate": 1.08,
        "last_updated": "2024-01-15T11:30:00Z",
        "source": "ECB"
    }
}
```

### Tüm Kurlar Response (target belirtilmezse)

```json
{
    "success": true,
    "data": {
        "base": "EUR",
        "rates": {
            "USD": {
                "rate": 1.08,
                "last_updated": "2024-01-15T11:30:00Z"
            },
            "MLGR": {
                "rate": 10000.0,
                "last_updated": "2024-01-15T11:30:00Z"
            }
        },
        "source": "ECB"
    }
}
```

---

## Ülke Listesi

Desteklenen ülkeleri listeler (KYC ve compliance için).

**Endpoint**: `GET /api/wallet/countries`  
**Authentication**: Gerekli değil

### Örnek İstek

```http
GET /api/wallet/countries
```

### Örnek Response

```json
{
    "success": true,
    "data": [
        {
            "code": "TR",
            "name": "Turkey",
            "name_tr": "Türkiye",
            "phone_code": "+90",
            "currency": "TRY",
            "is_supported": true,
            "kyc_required": true
        },
        {
            "code": "DE",
            "name": "Germany",
            "name_tr": "Almanya",
            "phone_code": "+49",
            "currency": "EUR",
            "is_supported": true,
            "kyc_required": false
        }
    ]
}
```

## Hata Durumları

### Geçersiz İşlem Tipi

```json
{
    "success": false,
    "message": "Operation parameter is required (deposit or withdraw)"
}
```

**HTTP Status**: `400 Bad Request`

### Desteklenmeyen Para Birimi

```json
{
    "success": false,
    "message": "Unsupported currency"
}
```

**HTTP Status**: `400 Bad Request`

### Eksik Parametre

```json
{
    "success": false,
    "message": "Account number is required"
}
```

**HTTP Status**: `422 Unprocessable Entity`

### Sistem Bakımda

```json
{
    "success": false,
    "message": "System is under maintenance"
}
```

**HTTP Status**: `503 Service Unavailable`

## Kullanım Notları

### Para Birimleri
- EUR varsayılan para birimidir
- MLGR özel kripto para birimidir
- Döviz kurları günlük güncellenir

### Ödeme Yöntemleri
- Her yöntemin kendine özgü limitleri vardır
- Ücretler işlem tipine göre değişir
- İşlem süreleri yönteme bağlıdır

### Hesap Numarası Doğrulama
- Luhn algoritması kullanılır
- Format kontrolü yapılır
- Veritabanında varlık kontrolü yapılır

### Sistem Durumu
- Real-time sağlık kontrolü
- Servis durumları ayrı ayrı izlenir
- Performans metrikleri dahildir
