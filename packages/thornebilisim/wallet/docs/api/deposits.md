# Para Yatırma İşlemleri

Müşterilerin cüzdanlarına para yatırması için kullanılan endpoint'ler.

## Para Yatırma Geçmişi

Müşterinin para yatırma işlemlerini listeler.

**Endpoint**: `GET /api/wallet/deposits`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `currency` | string | Hayır | Para birimi filtresi (EUR, USD, MLGR) |
| `status` | string | Hayır | Durum filtresi (pending, completed, failed, cancelled) |
| `method` | string | Hayır | Ödeme yöntemi filtresi |
| `from_date` | string | Hayır | Başlangıç tarihi (YYYY-MM-DD) |
| `to_date` | string | Hayır | <PERSON><PERSON><PERSON> tarihi (YYYY-MM-DD) |
| `per_page` | integer | Hayır | <PERSON><PERSON> başı<PERSON> ka<PERSON>ı<PERSON> (varsayılan: 15) |

### Örnek İstek

```http
GET /api/wallet/deposits?currency=EUR&status=completed&per_page=10
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "transaction_id": "TXN_DEP_001",
            "method": "bank_transfer",
            "currency": "EUR",
            "amount": "500.00",
            "status": "completed",
            "description": "Hesaba para yatırma",
            "created_at": "2024-01-15T10:00:00Z",
            "completed_at": "2024-01-15T10:30:00Z",
            "metadata": {
                "ip_address": "***********",
                "user_agent": "Mozilla/5.0..."
            }
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 3,
        "per_page": 10,
        "total": 25,
        "from": 1,
        "to": 10
    }
}
```

---

## Yeni Para Yatırma

Yeni para yatırma işlemi oluşturur.

**Endpoint**: `POST /api/wallet/deposits`  
**Authentication**: Gerekli

### Request Body

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `method` | string | Evet | Ödeme yöntemi (bank_transfer, paypal, qonto, cash) |
| `currency` | string | Evet | Para birimi (3 karakter) |
| `amount` | number | Evet | Miktar (minimum: 0.01) |
| `description` | string | Hayır | Açıklama (maksimum: 255 karakter) |

### Örnek İstek

```http
POST /api/wallet/deposits
Authorization: Bearer {token}
Content-Type: application/json

{
    "method": "bank_transfer",
    "currency": "EUR",
    "amount": 500.00,
    "description": "Hesaba para yatırma"
}
```

### Başarılı Response

```json
{
    "success": true,
    "data": {
        "id": 2,
        "transaction_id": "TXN_DEP_002",
        "method": "bank_transfer",
        "currency": "EUR",
        "amount": "500.00",
        "status": "pending",
        "description": "Hesaba para yatırma",
        "created_at": "2024-01-15T11:00:00Z",
        "company_accounts": [
            {
                "bank_name": "Example Bank",
                "account_holder": "Company Name",
                "iban": "**************************",
                "account_number": "**********",
                "swift": "EXMPTRAA"
            }
        ]
    }
}
```

---

## Para Yatırma Yöntemleri

Müşteri için kullanılabilir para yatırma yöntemlerini listeler.

**Endpoint**: `GET /api/wallet/deposits/methods`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `currency` | string | Hayır | Para birimi filtresi |

### Örnek İstek

```http
GET /api/wallet/deposits/methods?currency=EUR
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "bank_transfer": {
            "name": "Banka Havalesi",
            "enabled": true,
            "can_deposit": true,
            "can_withdraw": false,
            "supported_currencies": ["EUR", "USD"],
            "min_amount": "10.00",
            "max_amount": "10000.00",
            "processing_time": "1-3 iş günü",
            "fees": {
                "fixed": "0.00",
                "percentage": "0.00"
            }
        },
        "paypal": {
            "name": "PayPal",
            "enabled": true,
            "can_deposit": true,
            "can_withdraw": true,
            "supported_currencies": ["EUR", "USD"],
            "min_amount": "5.00",
            "max_amount": "5000.00",
            "processing_time": "Anında",
            "fees": {
                "fixed": "0.30",
                "percentage": "2.90"
            }
        }
    }
}
```

---

## Şirket Hesapları

Para yatırma için şirket hesap bilgilerini getirir.

**Endpoint**: `GET /api/wallet/deposits/company-accounts`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `method` | string | Evet | Ödeme yöntemi |
| `currency` | string | Evet | Para birimi |

### Örnek İstek

```http
GET /api/wallet/deposits/company-accounts?method=bank_transfer&currency=EUR
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": [
        {
            "bank_name": "Example Bank",
            "account_holder": "Company Name",
            "iban": "**************************",
            "account_number": "**********",
            "swift": "EXMPTRAA",
            "branch": "Merkez Şube",
            "address": "İstanbul, Türkiye"
        },
        {
            "bank_name": "Another Bank",
            "account_holder": "Company Name",
            "iban": "**************************",
            "account_number": "**********",
            "swift": "ANTRTRAA",
            "branch": "Ana Şube",
            "address": "Ankara, Türkiye"
        }
    ]
}
```

---

## Para Yatırma Detayı

Belirli bir para yatırma işleminin detaylarını getirir.

**Endpoint**: `GET /api/wallet/deposits/{transaction_id}`  
**Authentication**: Gerekli

### Örnek İstek

```http
GET /api/wallet/deposits/1
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "id": 1,
        "transaction_id": "TXN_DEP_001",
        "method": "bank_transfer",
        "currency": "EUR",
        "amount": "500.00",
        "status": "completed",
        "description": "Hesaba para yatırma",
        "created_at": "2024-01-15T10:00:00Z",
        "completed_at": "2024-01-15T10:30:00Z",
        "metadata": {
            "ip_address": "***********",
            "user_agent": "Mozilla/5.0...",
            "reference_number": "REF123456"
        },
        "company_accounts": [
            {
                "bank_name": "Example Bank",
                "account_holder": "Company Name",
                "iban": "**************************"
            }
        ]
    }
}
```

## Hata Durumları

### Geçersiz Ödeme Yöntemi

```json
{
    "success": false,
    "message": "Invalid payment method"
}
```

**HTTP Status**: `400 Bad Request`

### Yetersiz Miktar

```json
{
    "success": false,
    "message": "Amount must be at least 0.01"
}
```

**HTTP Status**: `422 Unprocessable Entity`

### İşlem Bulunamadı

```json
{
    "success": false,
    "message": "Deposit not found"
}
```

**HTTP Status**: `404 Not Found`

### Eksik Parametreler

```json
{
    "success": false,
    "message": "Method and currency are required"
}
```

**HTTP Status**: `400 Bad Request`

## İşlem Durumları

- `pending` - Beklemede
- `processing` - İşleniyor
- `completed` - Tamamlandı
- `failed` - Başarısız
- `cancelled` - İptal edildi

## Ödeme Yöntemleri

- `bank_transfer` - Banka Havalesi
- `paypal` - PayPal
- `qonto` - Qonto
- `cash` - Nakit
