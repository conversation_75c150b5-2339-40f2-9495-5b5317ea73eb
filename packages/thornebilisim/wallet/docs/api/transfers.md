# Transfer İşlemleri

Müşteriler arası para transferi için k<PERSON> endpoint'ler.

## Transfer Geçmişi

Müşterinin gönderdiği ve aldığı transferleri listeler.

**Endpoint**: `GET /api/wallet/transfers`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `type` | string | Hayır | Transfer tipi (sent, received, all) - varsayılan: all |
| `status` | string | Hayır | Durum filtresi (pending, completed, failed, cancelled) |
| `from_date` | string | Hayır | Başlangıç tarihi (YYYY-MM-DD) |
| `to_date` | string | Hayır | Bitiş tarihi (YYYY-MM-DD) |
| `per_page` | integer | Hayır | <PERSON><PERSON> ba<PERSON>ı<PERSON> ka<PERSON> (varsayılan: 15) |

### <PERSON>rnek İstek

```http
GET /api/wallet/transfers?type=sent&status=completed&per_page=10
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "transfer_id": "TRF_001",
            "type": "sent",
            "from_account_number": "EUR1234567",
            "to_account_number": "EUR9876543",
            "amount": "100.00",
            "currency": "EUR",
            "status": "completed",
            "description": "Arkadaşa para gönderimi",
            "recipient_name": "Jane Doe",
            "created_at": "2024-01-15T10:00:00Z",
            "completed_at": "2024-01-15T10:01:00Z"
        },
        {
            "id": 2,
            "transfer_id": "TRF_002",
            "type": "received",
            "from_account_number": "EUR5555555",
            "to_account_number": "EUR1234567",
            "amount": "50.00",
            "currency": "EUR",
            "status": "completed",
            "description": "Borç ödeme",
            "sender_name": "Bob Smith",
            "created_at": "2024-01-14T15:30:00Z",
            "completed_at": "2024-01-14T15:31:00Z"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 3,
        "per_page": 10,
        "total": 25,
        "from": 1,
        "to": 10
    }
}
```

---

## Yeni Transfer

Başka bir müşteriye para transferi yapar.

**Endpoint**: `POST /api/wallet/transfers`  
**Authentication**: Gerekli

### Request Body

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `from_account_number` | string | Evet | Gönderen hesap numarası (11 karakter) |
| `to_account_number` | string | Evet | Alıcı hesap numarası (11 karakter) |
| `amount` | number | Evet | Transfer miktarı (minimum: 0.01) |
| `description` | string | Hayır | Transfer açıklaması (maksimum: 255 karakter) |

### Örnek İstek

```http
POST /api/wallet/transfers
Authorization: Bearer {token}
Content-Type: application/json

{
    "from_account_number": "EUR1234567",
    "to_account_number": "EUR9876543",
    "amount": 100.00,
    "description": "Arkadaşa para gönderimi"
}
```

### Başarılı Response

```json
{
    "success": true,
    "data": {
        "id": 3,
        "transfer_id": "TRF_003",
        "from_account_number": "EUR1234567",
        "to_account_number": "EUR9876543",
        "amount": "100.00",
        "currency": "EUR",
        "status": "completed",
        "description": "Arkadaşa para gönderimi",
        "recipient_name": "Jane Doe",
        "fees": {
            "fixed": "0.00",
            "percentage": "0.00",
            "total": "0.00"
        },
        "created_at": "2024-01-15T11:00:00Z",
        "completed_at": "2024-01-15T11:01:00Z"
    }
}
```

---

## Hesap Doğrulama

Transfer öncesi alıcı hesabın geçerliliğini kontrol eder.

**Endpoint**: `GET /api/wallet/transfers/validate-account`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `account_number` | string | Evet | Doğrulanacak hesap numarası (11 karakter) |

### Örnek İstek

```http
GET /api/wallet/transfers/validate-account?account_number=EUR9876543
Authorization: Bearer {token}
```

### Geçerli Hesap Response

```json
{
    "success": true,
    "data": {
        "is_valid": true,
        "account_number": "EUR9876543",
        "formatted_account_number": "EUR-9876543",
        "currency": "EUR",
        "account_holder": "Jane Doe",
        "can_receive_transfers": true
    }
}
```

### Geçersiz Hesap Response

```json
{
    "success": false,
    "message": "Account not found or inactive"
}
```

**HTTP Status**: `404 Not Found`

---

## Transfer Detayı

Belirli bir transferin detaylarını getirir.

**Endpoint**: `GET /api/wallet/transfers/{transfer_id}`  
**Authentication**: Gerekli

### Örnek İstek

```http
GET /api/wallet/transfers/1
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "id": 1,
        "transfer_id": "TRF_001",
        "type": "sent",
        "from_account_number": "EUR1234567",
        "to_account_number": "EUR9876543",
        "amount": "100.00",
        "currency": "EUR",
        "status": "completed",
        "description": "Arkadaşa para gönderimi",
        "sender_name": "John Doe",
        "recipient_name": "Jane Doe",
        "fees": {
            "fixed": "0.00",
            "percentage": "0.00",
            "total": "0.00"
        },
        "created_at": "2024-01-15T10:00:00Z",
        "completed_at": "2024-01-15T10:01:00Z",
        "metadata": {
            "ip_address": "***********",
            "user_agent": "Mozilla/5.0...",
            "reference_number": "REF456789"
        }
    }
}
```

---

## Transfer İptali

Beklemede olan transferi iptal eder.

**Endpoint**: `POST /api/wallet/transfers/{transfer_id}/cancel`  
**Authentication**: Gerekli

### Örnek İstek

```http
POST /api/wallet/transfers/1/cancel
Authorization: Bearer {token}
```

### Başarılı Response

```json
{
    "success": true,
    "data": {
        "id": 1,
        "transfer_id": "TRF_001",
        "status": "cancelled",
        "cancelled_at": "2024-01-15T11:30:00Z",
        "refund_amount": "100.00",
        "message": "Transfer successfully cancelled and amount refunded"
    }
}
```

## Hata Durumları

### Geçersiz Hesap Numarası

```json
{
    "success": false,
    "message": "Invalid from_account_number"
}
```

**HTTP Status**: `400 Bad Request`

### Yetersiz Bakiye

```json
{
    "success": false,
    "message": "Insufficient balance"
}
```

**HTTP Status**: `400 Bad Request`

### Aynı Hesaba Transfer

```json
{
    "success": false,
    "message": "Cannot transfer to the same account"
}
```

**HTTP Status**: `400 Bad Request`

### Farklı Para Birimi

```json
{
    "success": false,
    "message": "Currency mismatch between accounts"
}
```

**HTTP Status**: `400 Bad Request`

### Transfer Bulunamadı

```json
{
    "success": false,
    "message": "Transfer not found"
}
```

**HTTP Status**: `404 Not Found`

### İptal Edilemeyen Transfer

```json
{
    "success": false,
    "message": "Transfer cannot be cancelled"
}
```

**HTTP Status**: `400 Bad Request`

### Hesap Sahibi Değil

```json
{
    "success": false,
    "message": "You don't own this account"
}
```

**HTTP Status**: `403 Forbidden`

## Transfer Durumları

- `pending` - Beklemede (onay bekleniyor)
- `processing` - İşleniyor
- `completed` - Tamamlandı
- `failed` - Başarısız
- `cancelled` - İptal edildi

## Transfer Tipleri

- `sent` - Gönderilen transferler
- `received` - Alınan transferler
- `all` - Tüm transferler

## Önemli Notlar

- Transferler aynı para birimi hesapları arasında yapılabilir
- Müşteri sadece kendi hesaplarından transfer gönderebilir
- Transfer ücretleri konfigürasyona göre uygulanır
- Büyük miktarlar ek onay gerektirebilir
- Transfer geçmişi audit log'unda tutulur

## Güvenlik

- Hesap numaraları Luhn algoritması ile doğrulanır
- Transfer limitleri müşteri bazında uygulanır
- Şüpheli işlemler otomatik olarak flaglenir
- Tüm transferler real-time monitoring'e tabidir
