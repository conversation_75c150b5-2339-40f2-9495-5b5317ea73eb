# Wallet API Dokümantasyonu

Bu dokümantasyon, Wallet paketinin API endpoint'lerini açıklar. API, müşterilerin cüzdan işlemlerini gerçekleştirmesini sağlar.

## Genel Bilgiler

- **Base URL**: `/api/wallet`
- **Content-Type**: `application/json`
- **Authentication**: Bearer <PERSON> (müşteri kimlik doğrulaması gerekli)

## API Grupları

### 🔐 [Kimlik Doğrulama](./authentication.md)
API'ye erişim için gerekli kimlik doğrulama bilgileri.

### 💼 [Cüzdan Genel Bakış](./wallet-overview.md)
- Cüzdan özeti
- Bakiye sorgulama
- Bakiye senkronizasyonu

### 🏦 [Hesap Yönetimi](./accounts.md)
- Hesap listeleme
- Yeni hesap oluşturma
- Hesap doğrulama

### 💰 [Para Yatırma](./deposits.md)
- Para yatırma işlemleri
- Yatırma yöntemleri
- Şirket hesapları

### 💸 [Para Çekme](./withdrawals.md)
- Para çekme işlemleri
- Çekme yöntemleri
- Çekme detayları

### 🔄 [Transfer İşlemleri](./transfers.md)
- Müşteriler arası transfer
- Transfer doğrulama
- Transfer iptali

### 📊 [İşlem Geçmişi](./transactions.md)
- İşlem listeleme
- İşlem detayları
- Filtreleme seçenekleri

### 🛠️ [Yardımcı Endpoint'ler](./utilities.md)
- Desteklenen para birimleri
- Ödeme yöntemleri
- Hesap numarası doğrulama

## Genel Response Formatı

Tüm API endpoint'leri aşağıdaki standart response formatını kullanır:

### Başarılı Response
```json
{
    "success": true,
    "data": {
        // Response verisi
    }
}
```

### Hata Response
```json
{
    "success": false,
    "message": "Hata mesajı"
}
```

### Sayfalama (Pagination)
Listeleme endpoint'lerinde sayfalama bilgisi:

```json
{
    "success": true,
    "data": [...],
    "pagination": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 15,
        "total": 75,
        "from": 1,
        "to": 15
    }
}
```

## HTTP Status Kodları

- `200` - Başarılı
- `400` - Geçersiz istek
- `401` - Kimlik doğrulama hatası
- `404` - Bulunamadı
- `422` - Validasyon hatası
- `500` - Sunucu hatası

## Para Birimleri

Desteklenen para birimleri:
- `EUR` - Euro (varsayılan)
- `USD` - Amerikan Doları
- `MLGR` - Özel para birimi

## Hesap Numarası Formatı

Hesap numaraları 11 haneli olup şu formatta oluşturulur:
- 3 hane: Para birimi kodu
- 1 hane: Kontrol hanesi
- 7 hane: Rastgele sayı

Örnek: `EUR1234567` (EUR para birimi için)

## Rate Limiting

API istekleri rate limiting'e tabidir. Aşırı kullanım durumunda `429` status kodu döner.
