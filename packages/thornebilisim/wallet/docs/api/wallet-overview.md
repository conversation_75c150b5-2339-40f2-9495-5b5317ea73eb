# Cüzdan Genel Bakış

Müşterinin cüzdan bilgilerini ve bakiyelerini görüntülemek için kullanılan endpoint'ler.

## Cüzdan Özeti

Müşterinin genel cüzdan bilgilerini getirir.

**Endpoint**: `GET /api/wallet/overview`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `sync` | boolean | Hayır | Web3 ile senkronizasyon yapılsın mı (varsayılan: false) |

### <PERSON>rnek İstek

```http
GET /api/wallet/overview?sync=true
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "customer_id": 123,
        "accounts": [
            {
                "currency": "EUR",
                "account_number": "EUR1234567",
                "balance": "1500.50",
                "locked_balance": "100.00",
                "available_balance": "1400.50"
            }
        ],
        "total_balances": {
            "EUR": "1500.50",
            "USD": "0.00"
        },
        "recent_transactions": [...],
        "last_sync": "2024-01-15T10:30:00Z"
    }
}
```

---

## Bakiye Sorgulama

Belirli bir para birimi için bakiye bilgilerini getirir.

**Endpoint**: `GET /api/wallet/balance`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `currency` | string | Hayır | Para birimi kodu (3 karakter) |
| `sync` | boolean | Hayır | Web3 ile senkronizasyon yapılsın mı |

### Örnek İstek

```http
GET /api/wallet/balance?currency=EUR&sync=false
Authorization: Bearer {token}
```

### Tek Para Birimi Response

```json
{
    "success": true,
    "data": {
        "currency": "EUR",
        "balance": "1500.50",
        "locked_balance": "100.00",
        "available_balance": "1400.50",
        "formatted_balance": "€1,500.50",
        "formatted_available_balance": "€1,400.50",
        "last_sync": "2024-01-15T10:30:00Z"
    }
}
```

### Tüm Para Birimleri Response (currency belirtilmezse)

```json
{
    "success": true,
    "data": {
        "EUR": {
            "currency": "EUR",
            "balance": "1500.50",
            "locked_balance": "100.00",
            "available_balance": "1400.50",
            "formatted_balance": "€1,500.50",
            "formatted_available_balance": "€1,400.50",
            "last_sync": "2024-01-15T10:30:00Z"
        },
        "USD": {
            "currency": "USD",
            "balance": "0.00",
            "locked_balance": "0.00",
            "available_balance": "0.00",
            "formatted_balance": "$0.00",
            "formatted_available_balance": "$0.00",
            "last_sync": null
        }
    }
}
```

---

## Bakiye Senkronizasyonu

Web3 ile bakiye senkronizasyonu yapar.

**Endpoint**: `POST /api/wallet/sync-balance`  
**Authentication**: Gerekli

### Request Body

```json
{
    "currency": "EUR"
}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "currency": "EUR",
        "old_balance": "1500.50",
        "new_balance": "1520.75",
        "difference": "20.25",
        "sync_time": "2024-01-15T10:35:00Z"
    }
}
```

---

## Bakiye Karşılaştırma

Veritabanı ve Web3 bakiyelerini karşılaştırır.

**Endpoint**: `GET /api/wallet/compare-balance`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `currency` | string | Evet | Para birimi kodu (3 karakter) |

### Örnek İstek

```http
GET /api/wallet/compare-balance?currency=EUR
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "currency": "EUR",
        "database_balance": "1500.50",
        "web3_balance": "1520.75",
        "difference": "20.25",
        "is_synced": false,
        "last_sync": "2024-01-15T10:30:00Z",
        "comparison_time": "2024-01-15T10:40:00Z"
    }
}
```

## Hata Durumları

### Desteklenmeyen Para Birimi

```json
{
    "success": false,
    "message": "Unsupported currency"
}
```

**HTTP Status**: `400 Bad Request`

### Senkronizasyon Hatası

```json
{
    "success": false,
    "message": "Failed to sync balance: Connection timeout"
}
```

**HTTP Status**: `500 Internal Server Error`
