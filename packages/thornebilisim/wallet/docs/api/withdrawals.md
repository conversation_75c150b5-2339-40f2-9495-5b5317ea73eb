# Para Çekme İşlemleri

Müşterilerin cüzdanlarından para çekmesi için kullanılan endpoint'ler.

## Para Çekme Geçmişi

Müşterinin para çekme işlemlerini listeler.

**Endpoint**: `GET /api/wallet/withdrawals`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `currency` | string | Hayır | Para birimi filtresi (EUR, USD, MLGR) |
| `status` | string | Hayır | Durum filtresi (pending, completed, failed, cancelled) |
| `method` | string | Hayır | Ödeme yöntemi filtresi |
| `from_date` | string | Hayır | Başlangıç tarihi (YYYY-MM-DD) |
| `to_date` | string | Hayır | <PERSON><PERSON><PERSON> tarihi (YYYY-MM-DD) |
| `per_page` | integer | Hayır | <PERSON><PERSON> b<PERSON> ka<PERSON> (varsayılan: 15) |

### Örnek İstek

```http
GET /api/wallet/withdrawals?currency=EUR&status=completed&per_page=10
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "transaction_id": "TXN_WTH_001",
            "method": "bank_transfer",
            "currency": "EUR",
            "amount": "200.00",
            "status": "completed",
            "description": "Hesaptan para çekme",
            "account_details": {
                "account_holder": "John Doe",
                "iban": "**************************",
                "bank_name": "Example Bank"
            },
            "created_at": "2024-01-15T10:00:00Z",
            "completed_at": "2024-01-15T12:00:00Z"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 2,
        "per_page": 10,
        "total": 15,
        "from": 1,
        "to": 10
    }
}
```

---

## Yeni Para Çekme

Yeni para çekme işlemi oluşturur.

**Endpoint**: `POST /api/wallet/withdrawals`  
**Authentication**: Gerekli

### Request Body

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `method` | string | Evet | Ödeme yöntemi (bank_transfer, paypal, qonto) |
| `currency` | string | Evet | Para birimi (3 karakter) |
| `amount` | number | Evet | Miktar (minimum: 0.01) |
| `account_details` | object | Evet | Hesap bilgileri |
| `description` | string | Hayır | Açıklama (maksimum: 255 karakter) |

### Account Details Formatı

#### Banka Havalesi için:
```json
{
    "account_holder": "John Doe",
    "iban": "**************************",
    "bank_name": "Example Bank"
}
```

#### PayPal için:
```json
{
    "email": "<EMAIL>"
}
```

### Örnek İstek

```http
POST /api/wallet/withdrawals
Authorization: Bearer {token}
Content-Type: application/json

{
    "method": "bank_transfer",
    "currency": "EUR",
    "amount": 200.00,
    "account_details": {
        "account_holder": "John Doe",
        "iban": "**************************",
        "bank_name": "Example Bank"
    },
    "description": "Hesaptan para çekme"
}
```

### Başarılı Response

```json
{
    "success": true,
    "data": {
        "id": 2,
        "transaction_id": "TXN_WTH_002",
        "method": "bank_transfer",
        "currency": "EUR",
        "amount": "200.00",
        "status": "pending",
        "description": "Hesaptan para çekme",
        "account_details": {
            "account_holder": "John Doe",
            "iban": "**************************",
            "bank_name": "Example Bank"
        },
        "created_at": "2024-01-15T11:00:00Z",
        "estimated_completion": "2024-01-17T11:00:00Z"
    }
}
```

---

## Para Çekme Yöntemleri

Müşteri için kullanılabilir para çekme yöntemlerini listeler.

**Endpoint**: `GET /api/wallet/withdrawals/methods`  
**Authentication**: Gerekli

### Query Parameters

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `currency` | string | Hayır | Para birimi filtresi |

### Örnek İstek

```http
GET /api/wallet/withdrawals/methods?currency=EUR
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "bank_transfer": {
            "name": "Banka Havalesi",
            "enabled": true,
            "can_deposit": false,
            "can_withdraw": true,
            "supported_currencies": ["EUR", "USD"],
            "min_amount": "10.00",
            "max_amount": "5000.00",
            "processing_time": "1-3 iş günü",
            "fees": {
                "fixed": "2.00",
                "percentage": "0.00"
            },
            "required_fields": [
                "account_holder",
                "iban",
                "bank_name"
            ]
        },
        "paypal": {
            "name": "PayPal",
            "enabled": true,
            "can_deposit": true,
            "can_withdraw": true,
            "supported_currencies": ["EUR", "USD"],
            "min_amount": "5.00",
            "max_amount": "2000.00",
            "processing_time": "Anında",
            "fees": {
                "fixed": "0.50",
                "percentage": "1.50"
            },
            "required_fields": [
                "email"
            ]
        }
    }
}
```

---

## Para Çekme Detayı

Belirli bir para çekme işleminin detaylarını getirir.

**Endpoint**: `GET /api/wallet/withdrawals/{transaction_id}`  
**Authentication**: Gerekli

### Örnek İstek

```http
GET /api/wallet/withdrawals/1
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": {
        "id": 1,
        "transaction_id": "TXN_WTH_001",
        "method": "bank_transfer",
        "currency": "EUR",
        "amount": "200.00",
        "status": "completed",
        "description": "Hesaptan para çekme",
        "account_details": {
            "account_holder": "John Doe",
            "iban": "**************************",
            "bank_name": "Example Bank"
        },
        "fees": {
            "fixed": "2.00",
            "percentage": "0.00",
            "total": "2.00"
        },
        "net_amount": "198.00",
        "created_at": "2024-01-15T10:00:00Z",
        "completed_at": "2024-01-15T12:00:00Z",
        "metadata": {
            "ip_address": "***********",
            "user_agent": "Mozilla/5.0...",
            "reference_number": "REF789012"
        }
    }
}
```

## Hata Durumları

### Yetersiz Bakiye

```json
{
    "success": false,
    "message": "Insufficient balance"
}
```

**HTTP Status**: `400 Bad Request`

### Geçersiz Hesap Bilgileri

```json
{
    "success": false,
    "message": "Invalid account details"
}
```

**HTTP Status**: `422 Unprocessable Entity`

### Minimum Miktar Hatası

```json
{
    "success": false,
    "message": "Amount must be at least 10.00 EUR"
}
```

**HTTP Status**: `422 Unprocessable Entity`

### Maksimum Miktar Hatası

```json
{
    "success": false,
    "message": "Amount cannot exceed 5000.00 EUR"
}
```

**HTTP Status**: `422 Unprocessable Entity`

### İşlem Bulunamadı

```json
{
    "success": false,
    "message": "Withdrawal not found"
}
```

**HTTP Status**: `404 Not Found`

### Geçersiz Ödeme Yöntemi

```json
{
    "success": false,
    "message": "Invalid withdrawal method"
}
```

**HTTP Status**: `400 Bad Request`

## İşlem Durumları

- `pending` - Beklemede (onay bekleniyor)
- `processing` - İşleniyor (ödeme yapılıyor)
- `completed` - Tamamlandı
- `failed` - Başarısız
- `cancelled` - İptal edildi

## Ödeme Yöntemleri

- `bank_transfer` - Banka Havalesi
- `paypal` - PayPal
- `qonto` - Qonto

## Güvenlik Notları

- Para çekme işlemleri ek güvenlik kontrollerinden geçer
- Büyük miktarlar manuel onay gerektirebilir
- Hesap bilgileri şifrelenerek saklanır
- İşlem geçmişi audit log'unda tutulur
