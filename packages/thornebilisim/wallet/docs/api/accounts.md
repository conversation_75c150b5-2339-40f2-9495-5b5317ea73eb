# Hesap Yönetimi

Müşteri hesaplarının listelenmesi ve yeni hesap oluşturulması için kullan<PERSON>lan endpoint'ler.

## Hesap Listeleme

Müşterinin aktif hesaplarını listeler.

**Endpoint**: `GET /api/wallet/accounts`  
**Authentication**: Gerekli

### Örnek İstek

```http
GET /api/wallet/accounts
Authorization: Bearer {token}
```

### Örnek Response

```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "currency": "EUR",
            "account_number": "EUR1234567",
            "formatted_account_number": "EUR-1234567",
            "is_active": true,
            "created_at": "2024-01-10T09:00:00Z"
        },
        {
            "id": 2,
            "currency": "USD",
            "account_number": "USD9876543",
            "formatted_account_number": "USD-9876543",
            "is_active": true,
            "created_at": "2024-01-12T14:30:00Z"
        }
    ]
}
```

---

## Yeni Hesap Oluşturma

Belirtilen para birimi için yeni hesap oluşturur.

**Endpoint**: `POST /api/wallet/accounts`  
**Authentication**: Gerekli

### Request Body

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `currency` | string | Evet | Para birimi kodu (3 karakter: EUR, USD, MLGR) |

### Örnek İstek

```http
POST /api/wallet/accounts
Authorization: Bearer {token}
Content-Type: application/json

{
    "currency": "EUR"
}
```

### Başarılı Response

```json
{
    "success": true,
    "data": {
        "id": 3,
        "currency": "EUR",
        "account_number": "EUR5678901",
        "formatted_account_number": "EUR-5678901",
        "is_active": true,
        "created_at": "2024-01-15T11:00:00Z"
    }
}
```

---

## Hesap Numarası Doğrulama (Public)

Verilen hesap numarasının geçerli olup olmadığını kontrol eder.

**Endpoint**: `POST /api/wallet/validate-account-number`  
**Authentication**: Gerekli değil

### Request Body

| Parametre | Tip | Zorunlu | Açıklama |
|-----------|-----|---------|----------|
| `account_number` | string | Evet | 11 haneli hesap numarası |

### Örnek İstek

```http
POST /api/wallet/validate-account-number
Content-Type: application/json

{
    "account_number": "EUR1234567"
}
```

### Geçerli Hesap Response

```json
{
    "success": true,
    "message": "Account is valid",
    "data": {
        "is_valid": true,
        "account_number": "EUR1234567",
        "formatted_account_number": "EUR-1234567",
        "currency": "EUR",
        "is_active": true
    }
}
```

### Geçersiz Hesap Response

```json
{
    "success": false,
    "message": "Invalid account number"
}
```

**HTTP Status**: `400 Bad Request`

## Hesap Numarası Formatı

Hesap numaraları aşağıdaki kurallara göre oluşturulur:

- **Toplam uzunluk**: 11 karakter
- **İlk 3 karakter**: Para birimi kodu (EUR, USD, MLGR)
- **4. karakter**: Kontrol hanesi (Luhn algoritması)
- **Son 7 karakter**: Rastgele sayı

### Örnek Hesap Numaraları

- `EUR1234567` - Euro hesabı
- `USD9876543` - Dolar hesabı
- `MLG5432109` - MLGR hesabı

## Hata Durumları

### Geçersiz Para Birimi

```json
{
    "success": false,
    "message": "Unsupported currency"
}
```

**HTTP Status**: `400 Bad Request`

### Hesap Zaten Mevcut

```json
{
    "success": false,
    "message": "Account already exists for this currency"
}
```

**HTTP Status**: `400 Bad Request`

### Validasyon Hatası

```json
{
    "success": false,
    "message": "The currency field is required."
}
```

**HTTP Status**: `422 Unprocessable Entity`
