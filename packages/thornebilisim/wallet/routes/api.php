<?php

use Illuminate\Support\Facades\Route;
use Thorne\Wallet\Http\Controllers\Api\WalletApiController;
use <PERSON>\Wallet\Http\Controllers\Api\DepositApiController;
use Thorne\Wallet\Http\Controllers\Api\WithdrawApiController;
use Thorne\Wallet\Http\Controllers\Api\TransferApiController;

/*
|--------------------------------------------------------------------------
| Wallet API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the wallet package. These routes are loaded
| by the WalletServiceProvider and will be assigned to the "api" middleware group.
|
*/

Route::middleware(['api'])
    ->prefix('api/wallet')
    ->name('api.wallet.')
    ->group(function () {
        
        // Public routes (no authentication required)
        Route::get('/currencies', [WalletApiController::class, 'currencies'])->name('currencies');
        Route::get('/payment-methods', [WalletApiController::class, 'paymentMethods'])->name('payment-methods');
        Route::post('/validate-account-number', [WalletApiController::class, 'validateAccountNumber'])->name('validate-account-number');
        
        // Authenticated customer routes
        Route::middleware(['customer'])->group(function () {
            
            // Wallet overview
            Route::get('/overview', [WalletApiController::class, 'overview'])->name('overview');
            Route::get('/balance', [WalletApiController::class, 'balance'])->name('balance');
            Route::post('/sync-balance', [WalletApiController::class, 'syncBalance'])->name('sync-balance');
            Route::get('/compare-balance', [WalletApiController::class, 'compareBalance'])->name('compare-balance');
            
            // Accounts
            Route::get('/accounts', [WalletApiController::class, 'accounts'])->name('accounts');
            Route::post('/accounts', [WalletApiController::class, 'createAccount'])->name('accounts.create');
            
            // Transactions
            Route::get('/transactions', [WalletApiController::class, 'transactions'])->name('transactions');
            Route::get('/transactions/{transaction}', [WalletApiController::class, 'showTransaction'])->name('transactions.show');
            
            // Deposits
            Route::prefix('deposits')->name('deposits.')->group(function () {
                Route::get('/', [DepositApiController::class, 'index'])->name('index');
                Route::post('/', [DepositApiController::class, 'store'])->name('store');
                Route::get('/methods', [DepositApiController::class, 'methods'])->name('methods');
                Route::get('/company-accounts', [DepositApiController::class, 'companyAccounts'])->name('company-accounts');
                Route::get('/{transaction}', [DepositApiController::class, 'show'])->name('show');
            });
            
            // Withdrawals
            Route::prefix('withdrawals')->name('withdrawals.')->group(function () {
                Route::get('/', [WithdrawApiController::class, 'index'])->name('index');
                Route::post('/', [WithdrawApiController::class, 'store'])->name('store');
                Route::get('/methods', [WithdrawApiController::class, 'methods'])->name('methods');
                Route::get('/{transaction}', [WithdrawApiController::class, 'show'])->name('show');
            });
            
            // Transfers
            Route::prefix('transfers')->name('transfers.')->group(function () {
                Route::get('/', [TransferApiController::class, 'index'])->name('index');
                Route::post('/', [TransferApiController::class, 'store'])->name('store');
                Route::get('/validate-account', [TransferApiController::class, 'validateAccount'])->name('validate-account');
                Route::get('/{transfer}', [TransferApiController::class, 'show'])->name('show');
                Route::post('/{transfer}/cancel', [TransferApiController::class, 'cancel'])->name('cancel');
            });
        });
    });
