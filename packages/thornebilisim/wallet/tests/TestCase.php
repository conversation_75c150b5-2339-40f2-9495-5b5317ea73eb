<?php

namespace Thorne\Wallet\Tests;

use Orchestra\Testbench\TestCase as Orchestra;
use Thorne\Wallet\Providers\WalletServiceProvider;

abstract class TestCase extends Orchestra
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
    }

    protected function getPackageProviders($app): array
    {
        return [
            WalletServiceProvider::class,
        ];
    }

    protected function getEnvironmentSetUp($app): void
    {
        config()->set('database.default', 'testing');
        config()->set('database.connections.testing', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);

        // Set wallet configuration for testing
        config()->set('wallet.enabled', true);
        config()->set('wallet.web3.enabled', false); // Disable WEB3 for testing
        config()->set('wallet.currencies', [
            'EUR' => [
                'iso_code' => '978',
                'name' => 'Euro',
                'symbol' => '€',
                'is_default' => true,
                'precision' => 2,
                'enabled' => true,
            ],
            'USD' => [
                'iso_code' => '840',
                'name' => 'US Dollar',
                'symbol' => '$',
                'is_default' => false,
                'precision' => 2,
                'enabled' => true,
            ],
            'MLGR' => [
                'iso_code' => '999',
                'name' => 'Miligram',
                'symbol' => 'MLGR',
                'is_default' => false,
                'precision' => 8,
                'enabled' => true,
            ],
        ]);
    }
}
