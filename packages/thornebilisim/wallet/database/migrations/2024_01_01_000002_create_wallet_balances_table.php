<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_balances', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('customer_id');
            $table->string('currency', 10); // EUR, USD, MLGR
            $table->decimal('balance', 20, 8)->default('0.00000000'); // Total balance
            $table->decimal('locked_balance', 20, 8)->default('0.00000000'); // Locked/reserved balance
            $table->timestamp('last_web3_sync')->nullable(); // Last sync with WEB3 service
            $table->json('web3_data')->nullable(); // Raw WEB3 response data
            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'currency']);
            $table->index(['currency']);
            $table->index(['last_web3_sync']);

            // Unique constraint: one balance record per customer per currency
            $table->unique(['customer_id', 'currency']);

            // Foreign key constraint
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_balances');
    }
};
