<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('customer_id');
            $table->string('currency', 10); // EUR, USD, MLGR
            $table->string('account_number', 11)->unique(); // 11-digit account number with Luhn
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['customer_id', 'currency']);
            $table->index(['account_number']);
            $table->index(['currency']);
            $table->index(['is_active']);

            // Unique constraint: one account per customer per currency
            $table->unique(['customer_id', 'currency']);

            // Foreign key constraint
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_accounts');
    }
};
