<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Wallet System Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the Thorne Bilişim Wallet
    | management system including multi-currency support, payment methods,
    | and authorization settings.
    |
    */

    'enabled' => env('WALLET_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    |
    | Define the currencies supported by the wallet system. Each currency
    | includes ISO code, name, symbol, and account number generation settings.
    |
    */
    'currencies' => [
        'EUR' => [
            'iso_code' => '978',
            'name' => 'Euro',
            'symbol' => '€',
            'is_default' => true,
            'precision' => 2,
            'enabled' => true,
        ],
        'USD' => [
            'iso_code' => '840',
            'name' => 'US Dollar',
            'symbol' => '$',
            'is_default' => false,
            'precision' => 2,
            'enabled' => true,
        ],
        'MLGR' => [
            'iso_code' => '999', // Custom code for MLGR
            'name' => 'Miligram',
            'symbol' => 'MLGR',
            'is_default' => false,
            'precision' => 8,
            'enabled' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Account Number Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for generating 11-digit account numbers using Luhn algorithm.
    | Format: [Currency ISO Code (3)] + [Check Digit (1)] + [Random (7)]
    |
    */
    'account_number' => [
        'total_length' => 11,
        'currency_code_length' => 3,
        'check_digit_length' => 1,
        'random_length' => 7,
        'use_luhn_algorithm' => true,
        'max_generation_attempts' => 100,
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Methods Configuration
    |--------------------------------------------------------------------------
    |
    | Define available payment methods for deposits and withdrawals.
    | Each method can be enabled/disabled for specific operations.
    |
    */
    'payment_methods' => [
        'cash' => [
            'name' => 'Cash',
            'enabled' => env('WALLET_CASH_ENABLED', false),
            'can_deposit' => false,
            'can_withdraw' => false,
            'supported_currencies' => ['EUR', 'USD'],
            'config' => [],
        ],
        'bank_transfer' => [
            'name' => 'Bank Transfer',
            'enabled' => env('WALLET_BANK_TRANSFER_ENABLED', true),
            'can_deposit' => true,
            'can_withdraw' => true,
            'supported_currencies' => ['EUR', 'USD'],
            'config' => [
                'min_amount' => 10.00,
                'max_amount' => 50000.00,
                'processing_time' => '1-3 business days',
            ],
        ],
        'qonto' => [
            'name' => 'Qonto',
            'enabled' => env('WALLET_QONTO_ENABLED', true),
            'can_deposit' => true,
            'can_withdraw' => true,
            'supported_currencies' => ['EUR'],
            'config' => [
                'min_amount' => 1.00,
                'max_amount' => 10000.00,
                'processing_time' => 'Instant',
            ],
        ],
        'paypal' => [
            'name' => 'PayPal',
            'enabled' => env('WALLET_PAYPAL_ENABLED', false),
            'can_deposit' => true,
            'can_withdraw' => false, // Example: Can deposit but not withdraw
            'supported_currencies' => ['EUR', 'USD'],
            'config' => [
                'min_amount' => 5.00,
                'max_amount' => 5000.00,
                'processing_time' => 'Instant',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Authorization & Permissions
    |--------------------------------------------------------------------------
    |
    | Configure permission-based restrictions for wallet operations.
    | These settings can be overridden per customer or payment method.
    |
    */
    'permissions' => [
        'deposit' => [
            'enabled' => true,
            'require_kyc' => env('WALLET_DEPOSIT_REQUIRE_KYC', true),
            'daily_limit' => env('WALLET_DEPOSIT_DAILY_LIMIT', 10000.00),
            'monthly_limit' => env('WALLET_DEPOSIT_MONTHLY_LIMIT', 50000.00),
        ],
        'withdraw' => [
            'enabled' => true,
            'require_kyc' => env('WALLET_WITHDRAW_REQUIRE_KYC', true),
            'daily_limit' => env('WALLET_WITHDRAW_DAILY_LIMIT', 5000.00),
            'monthly_limit' => env('WALLET_WITHDRAW_MONTHLY_LIMIT', 25000.00),
            'require_approval' => env('WALLET_WITHDRAW_REQUIRE_APPROVAL', false),
        ],
        'transfer' => [
            'enabled' => true,
            'require_kyc' => env('WALLET_TRANSFER_REQUIRE_KYC', true),
            'daily_limit' => env('WALLET_TRANSFER_DAILY_LIMIT', 2000.00),
            'monthly_limit' => env('WALLET_TRANSFER_MONTHLY_LIMIT', 10000.00),
            'min_amount' => env('WALLET_TRANSFER_MIN_AMOUNT', 1.00),
            'max_amount' => env('WALLET_TRANSFER_MAX_AMOUNT', 1000.00),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | WEB3 Integration
    |--------------------------------------------------------------------------
    |
    | Configuration for WEB3 service integration for balance management.
    |
    */
    'web3' => [
        'enabled' => env('WALLET_WEB3_ENABLED', true),
        'base_url' => env('WALLET_WEB3_BASE_URL', 'https://api.example.com'),
        'tenant_id' => env('WALLET_WEB3_TENANT_ID', 50),
        'chain_id' => env('WALLET_WEB3_CHAIN_ID', 'mirum-testnet'),
        'cache_ttl' => env('WALLET_WEB3_CACHE_TTL', 1800), // 30 minutes
        'timeout' => env('WALLET_WEB3_TIMEOUT', 30),
        'retry_attempts' => env('WALLET_WEB3_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Company Account Information
    |--------------------------------------------------------------------------
    |
    | Static company account information for deposits. This can also be
    | retrieved from an API endpoint if needed.
    |
    */
    'company_accounts' => [
        'bank_transfer' => [
            'EUR' => [
                'bank_name' => 'Example Bank',
                'account_holder' => 'Thorne Bilişim Ltd.',
                'iban' => 'TR00 0000 0000 0000 0000 0000 00',
                'swift' => 'EXAMPLETR',
                'currency' => 'EUR',
                'reference_required' => true,
            ],
            'USD' => [
                'bank_name' => 'Example Bank USD',
                'account_holder' => 'Thorne Bilişim Ltd.',
                'account_number' => '*********0',
                'routing_number' => '*********',
                'swift' => 'EXAMPLEUS',
                'currency' => 'USD',
                'reference_required' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Transaction Settings
    |--------------------------------------------------------------------------
    |
    | General transaction settings and limits.
    |
    */
    'transactions' => [
        'reference_prefix' => env('WALLET_REFERENCE_PREFIX', 'WLT-'),
        'pending_timeout' => env('WALLET_PENDING_TIMEOUT', 24), // hours
        'auto_approve_deposits' => env('WALLET_AUTO_APPROVE_DEPOSITS', false),
        'auto_approve_withdrawals' => env('WALLET_AUTO_APPROVE_WITHDRAWALS', false),
        'require_unique_reference' => env('WALLET_REQUIRE_UNIQUE_REFERENCE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configure logging for wallet operations.
    |
    */
    'logging' => [
        'enabled' => env('WALLET_LOGGING_ENABLED', true),
        'channel' => env('WALLET_LOG_CHANNEL', 'wallet'),
        'level' => env('WALLET_LOG_LEVEL', 'info'),
        'log_balance_checks' => env('WALLET_LOG_BALANCE_CHECKS', true),
        'log_transactions' => env('WALLET_LOG_TRANSACTIONS', true),
    ],
];
