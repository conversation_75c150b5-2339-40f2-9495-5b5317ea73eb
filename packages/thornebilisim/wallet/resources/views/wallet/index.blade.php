@extends('fast-order::layouts.miligold-default')

@section('body')
    <div class="account-content">
        <div class="account-layout">
            <div class="account-head">
                <span class="account-heading">
                    {{ __('wallet::app.wallet.title') }}
                </span>

                <div class="account-action">
                    <button type="button" class="btn btn-primary" @click="syncAllBalances">
                        {{ __('wallet::app.wallet.sync_balances') }}
                    </button>
                </div>

                <div class="horizontal-rule"></div>
            </div>

            <div class="account-table-content">
                <wallet-overview></wallet-overview>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14"></script>
    <script type="text/x-template" id="wallet-overview-template">
        <div class="wallet-overview">
            <!-- Balance Cards -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3>{{ __('wallet::app.wallet.balances') }}</h3>
                </div>
                <div v-for="(currency, code) in overview.currencies" :key="code" class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">@{{ code }}</h5>
                            <p class="card-text">
                                <strong>{{ __('wallet::app.wallet.balance') }}:</strong> @{{ currency.formatted_balance }}<br>
                                <strong>{{ __('wallet::app.wallet.available') }}:</strong> @{{ currency.formatted_available_balance }}<br>
                                <small class="text-muted">{{ __('wallet::app.wallet.account') }}: @{{ currency.formatted_account_number }}</small>
                            </p>
                            <button class="btn btn-sm btn-outline-primary" @click="syncBalance(code)">
                                {{ __('wallet::app.wallet.sync') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3>{{ __('wallet::app.wallet.quick_actions') }}</h3>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="#" class="btn btn-success btn-block" onclick="alert('Deposit feature coming soon')">
                        {{ __('wallet::app.wallet.deposit') }}
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="#" class="btn btn-warning btn-block" onclick="alert('Withdraw feature coming soon')">
                        {{ __('wallet::app.wallet.withdraw') }}
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="#" class="btn btn-info btn-block" onclick="alert('Transfer feature coming soon')">
                        {{ __('wallet::app.wallet.transfer') }}
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="#" class="btn btn-secondary btn-block" onclick="alert('Transaction history coming soon')">
                        {{ __('wallet::app.wallet.history') }}
                    </a>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="text-center">
                <div class="spinner-border" role="status">
                    <span class="sr-only">{{ __('wallet::app.wallet.loading') }}</span>
                </div>
            </div>
        </div>
    </script>

    <script>
        Vue.component('wallet-overview', {
            template: '#wallet-overview-template',

            data() {
                return {
                    overview: @json($overview),
                    loading: false
                };
            },

            methods: {
                async syncBalance(currency) {
                    this.loading = true;

                    try {
                        const response = await axios.post('{{ route("customer.account.wallet.sync-balance") }}', {
                            currency: currency
                        });

                        if (response.data.success) {
                            // Update the specific currency balance
                            this.overview.currencies[currency] = {
                                ...this.overview.currencies[currency],
                                ...response.data.data
                            };

                            this.$toast.success('{{ __("wallet::app.wallet.balance_synced") }}');
                        } else {
                            this.$toast.error(response.data.message || '{{ __("wallet::app.wallet.sync_failed") }}');
                        }
                    } catch (error) {
                        console.error('Sync error:', error);
                        this.$toast.error('{{ __("wallet::app.wallet.sync_failed") }}');
                    } finally {
                        this.loading = false;
                    }
                },

                async syncAllBalances() {
                    this.loading = true;

                    try {
                        const response = await axios.get('{{ route("customer.account.wallet.overview") }}?sync=1');

                        if (response.data.success) {
                            this.overview = response.data.data;
                            this.$toast.success('{{ __("wallet::app.wallet.all_balances_synced") }}');
                        } else {
                            this.$toast.error('{{ __("wallet::app.wallet.sync_failed") }}');
                        }
                    } catch (error) {
                        console.error('Sync error:', error);
                        this.$toast.error('{{ __("wallet::app.wallet.sync_failed") }}');
                    } finally {
                        this.loading = false;
                    }
                }
            }
        });
    </script>
@endpush
