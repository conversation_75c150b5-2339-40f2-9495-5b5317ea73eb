@extends('shop::layouts.master')

@section('page_title')
    {{ __('wallet::app.deposits.create') }}
@endsection

@section('content-wrapper')
    <div class="account-content">
        <div class="account-layout">
            @include('shop::customers.account.partials.sidenav')

            <div class="account-head">
                <span class="account-heading">
                    {{ __('wallet::app.deposits.create') }}
                </span>

                <div class="account-action">
                    <a href="{{ route('customer.account.wallet.deposit.index') }}" class="btn btn-secondary">
                        {{ __('wallet::app.back') }}
                    </a>
                </div>

                <div class="horizontal-rule"></div>
            </div>

            <div class="account-table-content">
                <deposit-form></deposit-form>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script type="text/x-template" id="deposit-form-template">
        <div class="deposit-form">
            <form @submit.prevent="submitForm" class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="method">{{ __('wallet::app.deposits.select_method') }}</label>
                        <select v-model="form.method" @change="onMethodChange" class="form-control" id="method" required>
                            <option value="">{{ __('wallet::app.deposits.select_method') }}</option>
                            <option v-for="(method, key) in methods" :key="key" :value="key">
                                @{{ method.name }}
                            </option>
                        </select>
                    </div>

                    <div class="form-group" v-if="form.method">
                        <label for="currency">{{ __('wallet::app.deposits.select_currency') }}</label>
                        <select v-model="form.currency" class="form-control" id="currency" required>
                            <option value="">{{ __('wallet::app.deposits.select_currency') }}</option>
                            <option v-for="currency in availableCurrencies" :key="currency" :value="currency">
                                @{{ currency }}
                            </option>
                        </select>
                    </div>

                    <div class="form-group" v-if="form.currency">
                        <label for="amount">{{ __('wallet::app.deposits.enter_amount') }}</label>
                        <input v-model="form.amount" type="number" step="0.01" min="0.01" class="form-control" id="amount" required>
                        <small class="form-text text-muted" v-if="selectedMethod">
                            {{ __('wallet::app.deposits.min_amount') }}: @{{ selectedMethod.config.min_amount || 'N/A' }}<br>
                            {{ __('wallet::app.deposits.max_amount') }}: @{{ selectedMethod.config.max_amount || 'N/A' }}<br>
                            {{ __('wallet::app.deposits.processing_time') }}: @{{ selectedMethod.config.processing_time || 'N/A' }}
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="description">{{ __('wallet::app.wallet.description') }}</label>
                        <textarea v-model="form.description" class="form-control" id="description" rows="3" placeholder="{{ __('wallet::app.optional') }}"></textarea>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" :disabled="loading">
                            <span v-if="loading" class="spinner-border spinner-border-sm mr-2" role="status"></span>
                            {{ __('wallet::app.deposits.submit') }}
                        </button>
                    </div>
                </div>

                <div class="col-md-6" v-if="form.method && form.currency">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('wallet::app.deposits.company_accounts') }}</h5>
                        </div>
                        <div class="card-body">
                            <div v-if="loadingAccounts" class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="sr-only">{{ __('wallet::app.wallet.loading') }}</span>
                                </div>
                            </div>
                            <div v-else-if="companyAccounts">
                                <h6>{{ __('wallet::app.deposits.transfer_to') }}:</h6>
                                <table class="table table-sm">
                                    <tr v-for="(value, key) in companyAccounts" :key="key">
                                        <td><strong>@{{ key.replace('_', ' ').toUpperCase() }}:</strong></td>
                                        <td>@{{ value }}</td>
                                    </tr>
                                </table>
                                <div class="alert alert-info mt-3">
                                    <small>
                                        {{ __('wallet::app.deposits.transfer_instructions') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </script>

    <script>
        Vue.component('deposit-form', {
            template: '#deposit-form-template',
            
            data() {
                return {
                    methods: @json($methods),
                    currencies: @json(array_keys($currencies)),
                    form: {
                        method: '{{ request()->get("method") }}',
                        currency: '',
                        amount: '',
                        description: ''
                    },
                    companyAccounts: null,
                    loading: false,
                    loadingAccounts: false
                };
            },

            computed: {
                selectedMethod() {
                    return this.form.method ? this.methods[this.form.method] : null;
                },

                availableCurrencies() {
                    if (!this.selectedMethod) return [];
                    return this.selectedMethod.supported_currencies.filter(currency => 
                        this.currencies.includes(currency)
                    );
                }
            },

            watch: {
                'form.currency'() {
                    this.loadCompanyAccounts();
                }
            },

            mounted() {
                if (this.form.method) {
                    this.onMethodChange();
                }
            },

            methods: {
                onMethodChange() {
                    this.form.currency = '';
                    this.companyAccounts = null;
                },

                async loadCompanyAccounts() {
                    if (!this.form.method || !this.form.currency) return;

                    this.loadingAccounts = true;
                    try {
                        const response = await axios.get('/api/wallet/deposits/company-accounts', {
                            params: {
                                method: this.form.method,
                                currency: this.form.currency
                            }
                        });

                        if (response.data.success) {
                            this.companyAccounts = response.data.data;
                        }
                    } catch (error) {
                        console.error('Error loading company accounts:', error);
                    } finally {
                        this.loadingAccounts = false;
                    }
                },

                async submitForm() {
                    this.loading = true;

                    try {
                        const response = await axios.post('/api/wallet/deposits', this.form);

                        if (response.data.success) {
                            this.$toast.success('{{ __("wallet::app.deposits.success") }}');
                            window.location.href = '{{ route("customer.account.wallet.deposit.show", "") }}/' + response.data.data.id;
                        } else {
                            this.$toast.error(response.data.message);
                        }
                    } catch (error) {
                        const message = error.response?.data?.message || '{{ __("wallet::app.deposits.failed") }}';
                        this.$toast.error(message);
                    } finally {
                        this.loading = false;
                    }
                }
            }
        });
    </script>
@endpush
