@extends('shop::layouts.master')

@section('page_title')
    {{ __('wallet::app.deposits.title') }}
@endsection

@section('content-wrapper')
    <div class="account-content">
        <div class="account-layout">
            @include('shop::customers.account.partials.sidenav')

            <div class="account-head">
                <span class="account-heading">
                    {{ __('wallet::app.deposits.title') }}
                </span>

                <div class="account-action">
                    <a href="{{ route('customer.account.wallet.deposit.create') }}" class="btn btn-primary">
                        {{ __('wallet::app.deposits.create') }}
                    </a>
                </div>

                <div class="horizontal-rule"></div>
            </div>

            <div class="account-table-content">
                <deposit-list></deposit-list>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script type="text/x-template" id="deposit-list-template">
        <div class="deposit-list">
            <div class="row mb-4">
                <div class="col-12">
                    <h4>{{ __('wallet::app.deposits.methods') }}</h4>
                    <div class="row">
                        <div v-for="(method, key) in methods" :key="key" class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">@{{ method.name }}</h5>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            {{ __('wallet::app.deposits.supported_currencies') }}: @{{ method.supported_currencies.join(', ') }}
                                        </small>
                                    </p>
                                    <a :href="'{{ route('customer.account.wallet.deposit.create') }}?method=' + key" class="btn btn-primary btn-sm">
                                        {{ __('wallet::app.deposits.create') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <h4>{{ __('wallet::app.deposits.recent') }}</h4>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{{ __('wallet::app.wallet.reference') }}</th>
                                    <th>{{ __('wallet::app.wallet.method') }}</th>
                                    <th>{{ __('wallet::app.wallet.currency') }}</th>
                                    <th>{{ __('wallet::app.wallet.amount') }}</th>
                                    <th>{{ __('wallet::app.wallet.status') }}</th>
                                    <th>{{ __('wallet::app.wallet.created_at') }}</th>
                                    <th>{{ __('wallet::app.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-if="loading">
                                    <td colspan="7" class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="sr-only">{{ __('wallet::app.wallet.loading') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr v-else-if="deposits.length === 0">
                                    <td colspan="7" class="text-center">
                                        {{ __('wallet::app.transactions.no_transactions') }}
                                    </td>
                                </tr>
                                <tr v-else v-for="deposit in deposits" :key="deposit.id">
                                    <td>@{{ deposit.reference }}</td>
                                    <td>@{{ deposit.method }}</td>
                                    <td>@{{ deposit.currency }}</td>
                                    <td>@{{ deposit.formatted_amount }}</td>
                                    <td>
                                        <span :class="'badge badge-' + getStatusColor(deposit.status)">
                                            @{{ deposit.status }}
                                        </span>
                                    </td>
                                    <td>@{{ formatDate(deposit.created_at) }}</td>
                                    <td>
                                        <a :href="'{{ route('customer.account.wallet.deposit.show', '') }}/' + deposit.id" class="btn btn-sm btn-outline-primary">
                                            {{ __('wallet::app.view') }}
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script>
        Vue.component('deposit-list', {
            template: '#deposit-list-template',
            
            data() {
                return {
                    methods: @json($methods),
                    deposits: [],
                    loading: true
                };
            },

            mounted() {
                this.loadDeposits();
            },

            methods: {
                async loadDeposits() {
                    try {
                        const response = await axios.get('/api/wallet/deposits');
                        if (response.data.success) {
                            this.deposits = response.data.data;
                        }
                    } catch (error) {
                        console.error('Error loading deposits:', error);
                    } finally {
                        this.loading = false;
                    }
                },

                getStatusColor(status) {
                    const colors = {
                        'pending': 'warning',
                        'processing': 'info',
                        'completed': 'success',
                        'failed': 'danger',
                        'cancelled': 'secondary'
                    };
                    return colors[status] || 'secondary';
                },

                formatDate(dateString) {
                    return new Date(dateString).toLocaleDateString();
                }
            }
        });
    </script>
@endpush
