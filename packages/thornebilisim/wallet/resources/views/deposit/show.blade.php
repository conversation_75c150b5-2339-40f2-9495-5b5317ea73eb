@extends('shop::layouts.master')

@section('page_title')
    {{ __('wallet::app.deposits.title') }} - {{ $transaction->reference }}
@endsection

@section('content-wrapper')
    <div class="account-content">
        <div class="account-layout">
            @include('shop::customers.account.partials.sidenav')

            <div class="account-head">
                <span class="account-heading">
                    {{ __('wallet::app.deposits.title') }} - {{ $transaction->reference }}
                </span>

                <div class="account-action">
                    <a href="{{ route('customer.account.wallet.deposit.index') }}" class="btn btn-secondary">
                        {{ __('wallet::app.back') }}
                    </a>
                </div>

                <div class="horizontal-rule"></div>
            </div>

            <div class="account-table-content">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5>{{ __('wallet::app.deposits.details') }}</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.reference') }}:</strong></td>
                                        <td>{{ $transaction->reference }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.status') }}:</strong></td>
                                        <td>
                                            <span class="badge badge-{{ $transaction->status->color() }}">
                                                {{ $transaction->status->label() }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.method') }}:</strong></td>
                                        <td>{{ $transaction->method }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.currency') }}:</strong></td>
                                        <td>{{ $transaction->currency }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.amount') }}:</strong></td>
                                        <td>{{ $transaction->getFormattedAmount() }}</td>
                                    </tr>
                                    @if($transaction->fee > 0)
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.fee') }}:</strong></td>
                                        <td>{{ $transaction->getFormattedFee() }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.net_amount') }}:</strong></td>
                                        <td>{{ $transaction->getFormattedNetAmount() }}</td>
                                    </tr>
                                    @endif
                                    @if($transaction->description)
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.description') }}:</strong></td>
                                        <td>{{ $transaction->description }}</td>
                                    </tr>
                                    @endif
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.created_at') }}:</strong></td>
                                        <td>{{ $transaction->created_at->format('d/m/Y H:i:s') }}</td>
                                    </tr>
                                    @if($transaction->processed_at)
                                    <tr>
                                        <td><strong>{{ __('wallet::app.wallet.processed_at') }}:</strong></td>
                                        <td>{{ $transaction->processed_at->format('d/m/Y H:i:s') }}</td>
                                    </tr>
                                    @endif
                                    @if($transaction->failure_reason)
                                    <tr>
                                        <td><strong>{{ __('wallet::app.failure_reason') }}:</strong></td>
                                        <td class="text-danger">{{ $transaction->failure_reason }}</td>
                                    </tr>
                                    @endif
                                </table>
                            </div>
                        </div>
                    </div>

                    @if($companyAccounts && $transaction->isPending())
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5>{{ __('wallet::app.deposits.payment_instructions') }}</h5>
                            </div>
                            <div class="card-body">
                                <h6>{{ __('wallet::app.deposits.transfer_to') }}:</h6>
                                <table class="table table-sm">
                                    @foreach($companyAccounts as $key => $value)
                                    <tr>
                                        <td><strong>{{ ucwords(str_replace('_', ' ', $key)) }}:</strong></td>
                                        <td>{{ $value }}</td>
                                    </tr>
                                    @endforeach
                                </table>

                                <div class="alert alert-warning mt-3">
                                    <h6>{{ __('wallet::app.deposits.important') }}:</h6>
                                    <ul class="mb-0">
                                        <li>{{ __('wallet::app.deposits.use_reference') }}: <strong>{{ $transaction->reference }}</strong></li>
                                        <li>{{ __('wallet::app.deposits.exact_amount') }}: <strong>{{ $transaction->getFormattedAmount() }}</strong></li>
                                        <li>{{ __('wallet::app.deposits.processing_time_note') }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
