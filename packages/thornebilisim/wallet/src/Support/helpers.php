<?php

if (! function_exists('wallet_config')) {
    /**
     * Get wallet configuration value.
     *
     * @param  string|null  $key
     * @param  mixed  $default
     * @return mixed
     */
    function wallet_config(?string $key = null, mixed $default = null): mixed
    {
        if ($key === null) {
            return config('wallet');
        }

        return config("wallet.{$key}", $default);
    }
}

if (! function_exists('wallet_currency_config')) {
    /**
     * Get currency configuration.
     *
     * @param  string  $currency
     * @param  string|null  $key
     * @param  mixed  $default
     * @return mixed
     */
    function wallet_currency_config(string $currency, ?string $key = null, mixed $default = null): mixed
    {
        $currencyConfig = wallet_config("currencies.{$currency}", []);

        if ($key === null) {
            return $currencyConfig;
        }

        return data_get($currencyConfig, $key, $default);
    }
}

if (! function_exists('wallet_payment_method_config')) {
    /**
     * Get payment method configuration.
     *
     * @param  string  $method
     * @param  string|null  $key
     * @param  mixed  $default
     * @return mixed
     */
    function wallet_payment_method_config(string $method, ?string $key = null, mixed $default = null): mixed
    {
        $methodConfig = wallet_config("payment_methods.{$method}", []);

        if ($key === null) {
            return $methodConfig;
        }

        return data_get($methodConfig, $key, $default);
    }
}

if (! function_exists('wallet_format_amount')) {
    /**
     * Format amount according to currency precision.
     *
     * @param  float|string  $amount
     * @param  string  $currency
     * @return string
     */
    function wallet_format_amount(float|string $amount, string $currency): string
    {
        $precision = wallet_currency_config($currency, 'precision', 2);
        
        return number_format((float) $amount, $precision, '.', '');
    }
}

if (! function_exists('wallet_generate_reference')) {
    /**
     * Generate a unique transaction reference.
     *
     * @param  string  $type
     * @return string
     */
    function wallet_generate_reference(string $type = 'TXN'): string
    {
        $prefix = wallet_config('transactions.reference_prefix', 'WLT-');
        $timestamp = now()->format('YmdHis');
        $random = strtoupper(substr(md5(uniqid()), 0, 6));
        
        return "{$prefix}{$type}-{$timestamp}-{$random}";
    }
}

if (! function_exists('wallet_is_supported_currency')) {
    /**
     * Check if currency is supported.
     *
     * @param  string  $currency
     * @return bool
     */
    function wallet_is_supported_currency(string $currency): bool
    {
        $currencies = wallet_config('currencies', []);
        
        return isset($currencies[$currency]) && $currencies[$currency]['enabled'] === true;
    }
}

if (! function_exists('wallet_get_default_currency')) {
    /**
     * Get the default currency.
     *
     * @return string|null
     */
    function wallet_get_default_currency(): ?string
    {
        $currencies = wallet_config('currencies', []);
        
        foreach ($currencies as $code => $config) {
            if ($config['is_default'] === true && $config['enabled'] === true) {
                return $code;
            }
        }
        
        return null;
    }
}

if (! function_exists('wallet_get_enabled_currencies')) {
    /**
     * Get all enabled currencies.
     *
     * @return array
     */
    function wallet_get_enabled_currencies(): array
    {
        $currencies = wallet_config('currencies', []);
        
        return array_filter($currencies, fn($config) => $config['enabled'] === true);
    }
}
