<?php

namespace Thorne\Wallet\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static int generateCheckDigit(string $number)
 * @method static bool validate(string $number)
 * @method static string generateWithCheckDigit(string $baseNumber)
 * @method static string generateRandomNumber(int $length)
 * @method static string generateAccountNumber(string $currencyIsoCode, int $randomLength = 7)
 * @method static bool validateAccountNumber(string $accountNumber, string|null $expectedCurrency = null)
 * @method static string|null extractCurrencyCode(string $accountNumber)
 * @method static int|null extractCheckDigit(string $accountNumber)
 * @method static string|null extractRandomPart(string $accountNumber)
 *
 * @see \Thorne\Wallet\Services\LuhnService
 */
class LuhnService extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'wallet.luhn';
    }
}
