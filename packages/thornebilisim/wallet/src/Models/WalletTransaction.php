<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Thorne\Wallet\Enums\TransactionDirection;
use Thorne\Wallet\Enums\TransactionStatus;
use Thorne\Wallet\Enums\TransactionType;
use Webkul\Customer\Models\Customer;

class WalletTransaction extends Model
{
    use HasFactory;

    protected $table = 'wallet_transactions';

    protected $fillable = [
        'customer_id',
        'account_number',
        'reference',
        'type',
        'direction',
        'status',
        'method',
        'currency',
        'amount',
        'fee',
        'net_amount',
        'exchange_rate',
        'description',
        'metadata',
        'external_reference',
        'external_transaction_id',
        'processed_at',
        'failed_at',
        'failure_reason',
        'parent_id',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'type' => TransactionType::class,
        'direction' => TransactionDirection::class,
        'status' => TransactionStatus::class,
        'amount' => 'decimal:8',
        'fee' => 'decimal:8',
        'net_amount' => 'decimal:8',
        'exchange_rate' => 'decimal:8',
        'metadata' => 'array',
        'processed_at' => 'datetime',
        'failed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the customer that owns this transaction.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the wallet account for this transaction.
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'account_number', 'account_number');
    }

    /**
     * Get the parent transaction.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Get child transactions.
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * Get related transfer if this is a transfer transaction.
     */
    public function transfer(): BelongsTo
    {
        return $this->belongsTo(WalletTransfer::class, 'reference', 'reference');
    }

    /**
     * Generate a unique reference for the transaction.
     *
     * @param  string  $type
     * @return string
     */
    public static function generateReference(string $type = 'TXN'): string
    {
        return wallet_generate_reference($type);
    }

    /**
     * Create a deposit transaction.
     *
     * @param  array  $data
     * @return static
     */
    public static function createDeposit(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('DEP');
        
        return static::create([
            'customer_id' => $data['customer_id'],
            'account_number' => $data['account_number'] ?? null,
            'reference' => $reference,
            'type' => TransactionType::DEPOSIT,
            'direction' => TransactionDirection::INBOUND,
            'status' => $data['status'] ?? TransactionStatus::PENDING,
            'method' => $data['method'],
            'currency' => $data['currency'],
            'amount' => $data['amount'],
            'fee' => $data['fee'] ?? '0.********',
            'net_amount' => $data['net_amount'] ?? $data['amount'],
            'description' => $data['description'] ?? null,
            'metadata' => $data['metadata'] ?? [],
            'external_reference' => $data['external_reference'] ?? null,
            'external_transaction_id' => $data['external_transaction_id'] ?? null,
        ]);
    }

    /**
     * Create a withdrawal transaction.
     *
     * @param  array  $data
     * @return static
     */
    public static function createWithdrawal(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('WTH');
        
        return static::create([
            'customer_id' => $data['customer_id'],
            'account_number' => $data['account_number'] ?? null,
            'reference' => $reference,
            'type' => TransactionType::WITHDRAWAL,
            'direction' => TransactionDirection::OUTBOUND,
            'status' => $data['status'] ?? TransactionStatus::PENDING,
            'method' => $data['method'],
            'currency' => $data['currency'],
            'amount' => $data['amount'],
            'fee' => $data['fee'] ?? '0.********',
            'net_amount' => $data['net_amount'] ?? $data['amount'],
            'description' => $data['description'] ?? null,
            'metadata' => $data['metadata'] ?? [],
            'external_reference' => $data['external_reference'] ?? null,
            'external_transaction_id' => $data['external_transaction_id'] ?? null,
        ]);
    }

    /**
     * Create a transfer transaction.
     *
     * @param  array  $data
     * @return static
     */
    public static function createTransfer(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('TRF');
        
        return static::create([
            'customer_id' => $data['customer_id'],
            'account_number' => $data['account_number'] ?? null,
            'reference' => $reference,
            'type' => TransactionType::TRANSFER,
            'direction' => $data['direction'],
            'status' => $data['status'] ?? TransactionStatus::PENDING,
            'method' => 'internal_transfer',
            'currency' => $data['currency'],
            'amount' => $data['amount'],
            'fee' => $data['fee'] ?? '0.********',
            'net_amount' => $data['net_amount'] ?? $data['amount'],
            'description' => $data['description'] ?? null,
            'metadata' => $data['metadata'] ?? [],
            'parent_id' => $data['parent_id'] ?? null,
        ]);
    }

    /**
     * Mark transaction as completed.
     *
     * @param  array  $data
     * @return bool
     */
    public function markAsCompleted(array $data = []): bool
    {
        return $this->update([
            'status' => TransactionStatus::COMPLETED,
            'processed_at' => now(),
            'external_transaction_id' => $data['external_transaction_id'] ?? $this->external_transaction_id,
            'metadata' => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
        ]);
    }

    /**
     * Mark transaction as failed.
     *
     * @param  string  $reason
     * @param  array  $data
     * @return bool
     */
    public function markAsFailed(string $reason, array $data = []): bool
    {
        return $this->update([
            'status' => TransactionStatus::FAILED,
            'failed_at' => now(),
            'failure_reason' => $reason,
            'metadata' => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
        ]);
    }

    /**
     * Mark transaction as cancelled.
     *
     * @param  string|null  $reason
     * @return bool
     */
    public function markAsCancelled(?string $reason = null): bool
    {
        return $this->update([
            'status' => TransactionStatus::CANCELLED,
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Check if transaction is pending.
     *
     * @return bool
     */
    public function isPending(): bool
    {
        return $this->status === TransactionStatus::PENDING;
    }

    /**
     * Check if transaction is completed.
     *
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === TransactionStatus::COMPLETED;
    }

    /**
     * Check if transaction is failed.
     *
     * @return bool
     */
    public function isFailed(): bool
    {
        return $this->status === TransactionStatus::FAILED;
    }

    /**
     * Check if transaction is cancelled.
     *
     * @return bool
     */
    public function isCancelled(): bool
    {
        return $this->status === TransactionStatus::CANCELLED;
    }

    /**
     * Get formatted amount for display.
     *
     * @return string
     */
    public function getFormattedAmount(): string
    {
        return wallet_format_amount($this->amount, $this->currency);
    }

    /**
     * Get formatted net amount for display.
     *
     * @return string
     */
    public function getFormattedNetAmount(): string
    {
        return wallet_format_amount($this->net_amount, $this->currency);
    }

    /**
     * Get formatted fee for display.
     *
     * @return string
     */
    public function getFormattedFee(): string
    {
        return wallet_format_amount($this->fee, $this->currency);
    }

    /**
     * Get transaction age in human readable format.
     *
     * @return string
     */
    public function getAge(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Scope to filter by transaction type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  TransactionType  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, TransactionType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by transaction status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  TransactionStatus  $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithStatus($query, TransactionStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by currency.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $currency
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to filter by method.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $method
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByMethod($query, string $method)
    {
        return $query->where('method', $method);
    }

    /**
     * Scope to filter pending transactions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', TransactionStatus::PENDING);
    }

    /**
     * Scope to filter completed transactions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', TransactionStatus::COMPLETED);
    }

    /**
     * Scope to filter failed transactions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', TransactionStatus::FAILED);
    }

    /**
     * Scope to filter transactions within date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \Carbon\Carbon  $from
     * @param  \Carbon\Carbon  $to
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInDateRange($query, Carbon $from, Carbon $to)
    {
        return $query->whereBetween('created_at', [$from, $to]);
    }
}
