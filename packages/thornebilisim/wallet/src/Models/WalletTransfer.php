<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Thorne\Wallet\Enums\TransactionDirection;
use Thorne\Wallet\Enums\TransactionStatus;
use Thorne\Wallet\Enums\TransferStatus;
use Webkul\Customer\Models\Customer;

class WalletTransfer extends Model
{
    use HasFactory;

    protected $table = 'wallet_transfers';

    protected $fillable = [
        'reference',
        'from_customer_id',
        'to_customer_id',
        'from_account_number',
        'to_account_number',
        'currency',
        'amount',
        'fee',
        'net_amount',
        'status',
        'description',
        'metadata',
        'processed_at',
        'failed_at',
        'failure_reason',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'status' => TransferStatus::class,
        'amount' => 'decimal:8',
        'fee' => 'decimal:8',
        'net_amount' => 'decimal:8',
        'metadata' => 'array',
        'processed_at' => 'datetime',
        'failed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the sender customer.
     */
    public function fromCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'from_customer_id');
    }

    /**
     * Get the recipient customer.
     */
    public function toCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'to_customer_id');
    }

    /**
     * Get the sender account.
     */
    public function fromAccount(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'from_account_number', 'account_number');
    }

    /**
     * Get the recipient account.
     */
    public function toAccount(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'to_account_number', 'account_number');
    }

    /**
     * Get all related transactions.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class, 'reference', 'reference');
    }

    /**
     * Get the outbound transaction (from sender).
     */
    public function outboundTransaction(): BelongsTo
    {
        return $this->belongsTo(WalletTransaction::class, 'reference', 'reference')
            ->where('direction', TransactionDirection::OUTBOUND);
    }

    /**
     * Get the inbound transaction (to recipient).
     */
    public function inboundTransaction(): BelongsTo
    {
        return $this->belongsTo(WalletTransaction::class, 'reference', 'reference')
            ->where('direction', TransactionDirection::INBOUND);
    }

    /**
     * Create a new transfer with transactions.
     *
     * @param  array  $data
     * @return static
     * @throws \Exception
     */
    public static function createTransfer(array $data): static
    {
        $reference = $data['reference'] ?? WalletTransaction::generateReference('TRF');
        
        // Validate accounts exist and are active
        $fromAccount = WalletAccount::findByAccountNumber($data['from_account_number']);
        $toAccount = WalletAccount::findByAccountNumber($data['to_account_number']);
        
        if (! $fromAccount || ! $toAccount) {
            throw new \InvalidArgumentException('Invalid account number(s)');
        }

        if ($fromAccount->currency !== $toAccount->currency) {
            throw new \InvalidArgumentException('Currency mismatch between accounts');
        }

        if ($data['from_customer_id'] === $data['to_customer_id']) {
            throw new \InvalidArgumentException('Cannot transfer to same customer');
        }

        \DB::beginTransaction();
        
        try {
            // Create transfer record
            $transfer = static::create([
                'reference' => $reference,
                'from_customer_id' => $data['from_customer_id'],
                'to_customer_id' => $data['to_customer_id'],
                'from_account_number' => $data['from_account_number'],
                'to_account_number' => $data['to_account_number'],
                'currency' => $fromAccount->currency,
                'amount' => $data['amount'],
                'fee' => $data['fee'] ?? '0.********',
                'net_amount' => $data['net_amount'] ?? $data['amount'],
                'status' => TransferStatus::PENDING,
                'description' => $data['description'] ?? null,
                'metadata' => $data['metadata'] ?? [],
            ]);

            // Create outbound transaction (sender)
            WalletTransaction::createTransfer([
                'customer_id' => $data['from_customer_id'],
                'account_number' => $data['from_account_number'],
                'reference' => $reference,
                'direction' => TransactionDirection::OUTBOUND,
                'currency' => $fromAccount->currency,
                'amount' => $data['amount'],
                'fee' => $data['fee'] ?? '0.********',
                'net_amount' => $data['net_amount'] ?? $data['amount'],
                'description' => $data['description'] ?? "Transfer to {$data['to_account_number']}",
                'metadata' => array_merge($data['metadata'] ?? [], [
                    'transfer_type' => 'outbound',
                    'recipient_account' => $data['to_account_number'],
                ]),
            ]);

            // Create inbound transaction (recipient)
            WalletTransaction::createTransfer([
                'customer_id' => $data['to_customer_id'],
                'account_number' => $data['to_account_number'],
                'reference' => $reference,
                'direction' => TransactionDirection::INBOUND,
                'currency' => $toAccount->currency,
                'amount' => $data['amount'],
                'fee' => '0.********', // Recipient doesn't pay fee
                'net_amount' => $data['amount'],
                'description' => $data['description'] ?? "Transfer from {$data['from_account_number']}",
                'metadata' => array_merge($data['metadata'] ?? [], [
                    'transfer_type' => 'inbound',
                    'sender_account' => $data['from_account_number'],
                ]),
            ]);

            \DB::commit();
            
            return $transfer;
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Complete the transfer.
     *
     * @param  array  $data
     * @return bool
     */
    public function complete(array $data = []): bool
    {
        \DB::beginTransaction();
        
        try {
            // Update transfer status
            $this->update([
                'status' => TransferStatus::COMPLETED,
                'processed_at' => now(),
                'metadata' => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
            ]);

            // Update related transactions
            $this->transactions()->update([
                'status' => TransactionStatus::COMPLETED,
                'processed_at' => now(),
            ]);

            \DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Fail the transfer.
     *
     * @param  string  $reason
     * @param  array  $data
     * @return bool
     */
    public function fail(string $reason, array $data = []): bool
    {
        \DB::beginTransaction();
        
        try {
            // Update transfer status
            $this->update([
                'status' => TransferStatus::FAILED,
                'failed_at' => now(),
                'failure_reason' => $reason,
                'metadata' => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
            ]);

            // Update related transactions
            $this->transactions()->update([
                'status' => TransactionStatus::FAILED,
                'failed_at' => now(),
                'failure_reason' => $reason,
            ]);

            \DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Cancel the transfer.
     *
     * @param  string|null  $reason
     * @return bool
     */
    public function cancel(?string $reason = null): bool
    {
        \DB::beginTransaction();
        
        try {
            // Update transfer status
            $this->update([
                'status' => TransferStatus::CANCELLED,
                'failure_reason' => $reason,
            ]);

            // Update related transactions
            $this->transactions()->update([
                'status' => TransactionStatus::CANCELLED,
                'failure_reason' => $reason,
            ]);

            \DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Check if transfer is pending.
     *
     * @return bool
     */
    public function isPending(): bool
    {
        return $this->status === TransferStatus::PENDING;
    }

    /**
     * Check if transfer is completed.
     *
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === TransferStatus::COMPLETED;
    }

    /**
     * Check if transfer is failed.
     *
     * @return bool
     */
    public function isFailed(): bool
    {
        return $this->status === TransferStatus::FAILED;
    }

    /**
     * Check if transfer is cancelled.
     *
     * @return bool
     */
    public function isCancelled(): bool
    {
        return $this->status === TransferStatus::CANCELLED;
    }

    /**
     * Get formatted amount for display.
     *
     * @return string
     */
    public function getFormattedAmount(): string
    {
        return wallet_format_amount($this->amount, $this->currency);
    }

    /**
     * Get formatted net amount for display.
     *
     * @return string
     */
    public function getFormattedNetAmount(): string
    {
        return wallet_format_amount($this->net_amount, $this->currency);
    }

    /**
     * Get formatted fee for display.
     *
     * @return string
     */
    public function getFormattedFee(): string
    {
        return wallet_format_amount($this->fee, $this->currency);
    }

    /**
     * Get transfer age in human readable format.
     *
     * @return string
     */
    public function getAge(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Scope to filter by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  TransferStatus  $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithStatus($query, TransferStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by currency.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $currency
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to filter pending transfers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', TransferStatus::PENDING);
    }

    /**
     * Scope to filter completed transfers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', TransferStatus::COMPLETED);
    }

    /**
     * Scope to filter failed transfers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', TransferStatus::FAILED);
    }

    /**
     * Scope to filter transfers involving a customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $customerId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCustomer($query, int $customerId)
    {
        return $query->where(function ($q) use ($customerId) {
            $q->where('from_customer_id', $customerId)
              ->orWhere('to_customer_id', $customerId);
        });
    }

    /**
     * Scope to filter outgoing transfers for a customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $customerId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOutgoingForCustomer($query, int $customerId)
    {
        return $query->where('from_customer_id', $customerId);
    }

    /**
     * Scope to filter incoming transfers for a customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $customerId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIncomingForCustomer($query, int $customerId)
    {
        return $query->where('to_customer_id', $customerId);
    }
}
