<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Thorne\Wallet\Services\LuhnService;
use Webkul\Customer\Models\Customer;

class WalletAccount extends Model
{
    use HasFactory;

    protected $table = 'wallet_accounts';

    protected $fillable = [
        'customer_id',
        'currency',
        'account_number',
        'is_active',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the customer that owns the wallet account.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get all transactions for this account.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class, 'account_number', 'account_number');
    }

    /**
     * Get all incoming transfers to this account.
     */
    public function incomingTransfers(): Has<PERSON>any
    {
        return $this->hasMany(WalletTransfer::class, 'to_account_number', 'account_number');
    }

    /**
     * Get all outgoing transfers from this account.
     */
    public function outgoingTransfers(): HasMany
    {
        return $this->hasMany(WalletTransfer::class, 'from_account_number', 'account_number');
    }

    /**
     * Get the balance for this account.
     */
    public function balance(): BelongsTo
    {
        return $this->belongsTo(WalletBalance::class, 'customer_id', 'customer_id')
            ->where('currency', $this->currency);
    }

    /**
     * Generate a unique account number for the given currency.
     *
     * @param  string  $currency
     * @param  int  $customerId
     * @return string
     * @throws \Exception
     */
    public static function generateAccountNumber(string $currency, int $customerId): string
    {
        $luhnService = app(LuhnService::class);
        $currencyConfig = wallet_currency_config($currency);
        
        if (empty($currencyConfig)) {
            throw new \InvalidArgumentException("Unsupported currency: {$currency}");
        }

        $isoCode = $currencyConfig['iso_code'];
        $maxAttempts = wallet_config('account_number.max_generation_attempts', 100);
        
        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            $accountNumber = $luhnService->generateAccountNumber($isoCode);
            
            // Check if account number already exists
            if (! static::where('account_number', $accountNumber)->exists()) {
                return $accountNumber;
            }
        }
        
        throw new \Exception("Failed to generate unique account number after {$maxAttempts} attempts");
    }

    /**
     * Validate account number format.
     *
     * @param  string  $accountNumber
     * @param  string|null  $expectedCurrency
     * @return bool
     */
    public static function validateAccountNumber(string $accountNumber, ?string $expectedCurrency = null): bool
    {
        $luhnService = app(LuhnService::class);
        
        if (! $luhnService->validateAccountNumber($accountNumber)) {
            return false;
        }

        if ($expectedCurrency !== null) {
            $currencyConfig = wallet_currency_config($expectedCurrency);
            if (empty($currencyConfig)) {
                return false;
            }
            
            $expectedIsoCode = $currencyConfig['iso_code'];
            $actualIsoCode = $luhnService->extractCurrencyCode($accountNumber);
            
            return $actualIsoCode === $expectedIsoCode;
        }

        return true;
    }

    /**
     * Get currency from account number.
     *
     * @param  string  $accountNumber
     * @return string|null
     */
    public static function getCurrencyFromAccountNumber(string $accountNumber): ?string
    {
        $luhnService = app(LuhnService::class);
        $isoCode = $luhnService->extractCurrencyCode($accountNumber);
        
        if ($isoCode === null) {
            return null;
        }

        $currencies = wallet_config('currencies', []);
        
        foreach ($currencies as $currency => $config) {
            if ($config['iso_code'] === $isoCode) {
                return $currency;
            }
        }

        return null;
    }

    /**
     * Find account by account number.
     *
     * @param  string  $accountNumber
     * @return static|null
     */
    public static function findByAccountNumber(string $accountNumber): ?static
    {
        return static::where('account_number', $accountNumber)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get or create account for customer and currency.
     *
     * @param  int  $customerId
     * @param  string  $currency
     * @return static
     * @throws \Exception
     */
    public static function getOrCreateForCustomer(int $customerId, string $currency): static
    {
        // Check if account already exists
        $existingAccount = static::where('customer_id', $customerId)
            ->where('currency', $currency)
            ->where('is_active', true)
            ->first();

        if ($existingAccount) {
            return $existingAccount;
        }

        // Create new account
        $accountNumber = static::generateAccountNumber($currency, $customerId);
        
        return static::create([
            'customer_id' => $customerId,
            'currency' => $currency,
            'account_number' => $accountNumber,
            'is_active' => true,
        ]);
    }

    /**
     * Deactivate the account.
     *
     * @return bool
     */
    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    /**
     * Activate the account.
     *
     * @return bool
     */
    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    /**
     * Check if account is active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->is_active === true;
    }

    /**
     * Get formatted account number for display.
     *
     * @return string
     */
    public function getFormattedAccountNumber(): string
    {
        $number = $this->account_number;
        
        // Format as: XXX-X-XXXXXXX (Currency-Check-Random)
        return substr($number, 0, 3) . '-' . substr($number, 3, 1) . '-' . substr($number, 4);
    }

    /**
     * Scope to filter by currency.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $currency
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to filter active accounts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
