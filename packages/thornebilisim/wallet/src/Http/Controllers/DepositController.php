<?php

namespace Thorne\Wallet\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Thorne\Wallet\Services\WalletService;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Enums\TransactionType;

class DepositController extends Controller
{
    public function __construct(
        protected WalletService $walletService
    ) {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check()) {
                return redirect()->route('customer.session.index');
            }
            return $next($request);
        });
    }

    /**
     * Display deposit page.
     */
    public function index(): View
    {
        $customer = auth()->guard('customer')->user();
        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'deposit');
        $currencies = wallet_get_enabled_currencies();
        
        return view('wallet::deposit.index', compact('methods', 'currencies'));
    }

    /**
     * Get available deposit methods.
     */
    public function methods(): JsonResponse
    {
        $customer = auth()->guard('customer')->user();
        $currency = request()->get('currency');
        
        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'deposit', $currency);
        
        return response()->json([
            'success' => true,
            'data' => $methods,
        ]);
    }

    /**
     * Show create deposit form.
     */
    public function create(): View
    {
        $customer = auth()->guard('customer')->user();
        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'deposit');
        $currencies = wallet_get_enabled_currencies();
        
        return view('wallet::deposit.create', compact('methods', 'currencies'));
    }

    /**
     * Store a new deposit request.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'method' => 'required|string',
            'currency' => 'required|string|size:3',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:255',
        ]);

        $customer = auth()->guard('customer')->user();

        try {
            $deposit = $this->walletService->createDeposit([
                'customer_id' => $customer->id,
                'method' => $request->method,
                'currency' => strtoupper($request->currency),
                'amount' => $request->amount,
                'description' => $request->description,
                'metadata' => [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
            ]);

            return redirect()
                ->route('customer.account.wallet.deposit.show', $deposit->id)
                ->with('success', __('wallet::app.deposits.success'));

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', $e->getMessage());
        }
    }

    /**
     * Show deposit details.
     */
    public function show(WalletTransaction $transaction): View
    {
        $customer = auth()->guard('customer')->user();

        if ($transaction->customer_id !== $customer->id || $transaction->type !== TransactionType::DEPOSIT) {
            abort(404);
        }

        $companyAccounts = $this->getCompanyAccountsForMethod($transaction->method, $transaction->currency);

        return view('wallet::deposit.show', compact('transaction', 'companyAccounts'));
    }

    /**
     * Get company accounts for deposits.
     */
    public function companyAccounts(): JsonResponse
    {
        $method = request()->get('method');
        $currency = request()->get('currency');

        if (! $method || ! $currency) {
            return response()->json([
                'success' => false,
                'message' => 'Method and currency are required',
            ], 400);
        }

        $accounts = $this->getCompanyAccountsForMethod($method, $currency);

        return response()->json([
            'success' => true,
            'data' => $accounts,
        ]);
    }

    /**
     * Get company accounts for specific method and currency.
     */
    protected function getCompanyAccountsForMethod(string $method, string $currency): array
    {
        $companyAccounts = wallet_config('company_accounts', []);
        
        return $companyAccounts[$method][$currency] ?? [];
    }
}
