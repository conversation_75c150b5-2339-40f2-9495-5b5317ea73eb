<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Services\WalletService;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Enums\TransactionType;

class DepositApiController extends Controller
{
    public function __construct(
        protected WalletService $walletService
    ) {
        $this->middleware(function ($request, $next) {
            if (! auth()->guard('customer')->check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthenticated',
                ], 401);
            }
            return $next($request);
        });
    }

    /**
     * Get customer deposits.
     */
    public function index(): JsonResponse
    {
        $customer = auth()->guard('customer')->user();

        $filters = [
            'currency' => request()->get('currency'),
            'status' => request()->get('status'),
            'method' => request()->get('method'),
            'from_date' => request()->get('from_date'),
            'to_date' => request()->get('to_date'),
            'per_page' => request()->get('per_page', 15),
        ];

        // Add type filter for deposits only
        $filters['type'] = TransactionType::DEPOSIT;

        $deposits = $this->walletService->getTransactionHistory($customer->id, $filters);

        return response()->json([
            'success' => true,
            'data' => $deposits->items(),
            'pagination' => [
                'current_page' => $deposits->currentPage(),
                'last_page' => $deposits->lastPage(),
                'per_page' => $deposits->perPage(),
                'total' => $deposits->total(),
                'from' => $deposits->firstItem(),
                'to' => $deposits->lastItem(),
            ],
        ]);
    }

    /**
     * Create a new deposit.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'method' => 'required|string',
            'currency' => 'required|string|size:3',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'nullable|string|max:255',
        ]);

        $customer = auth()->guard('customer')->user();

        try {
            $deposit = $this->walletService->createDeposit([
                'customer_id' => $customer->id,
                'method' => $request->input('method'),
                'currency' => strtoupper($request->currency),
                'amount' => $request->amount,
                'description' => $request->description,
                'metadata' => [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Deposit request created successfully',
                'data' => [
                    'id' => $deposit->id,
                    'reference' => $deposit->reference,
                    'type' => $deposit->type->value,
                    'status' => $deposit->status->value,
                    'method' => $deposit->method,
                    'currency' => $deposit->currency,
                    'amount' => $deposit->amount,
                    'formatted_amount' => $deposit->getFormattedAmount(),
                    'description' => $deposit->description,
                    'created_at' => $deposit->created_at->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get available deposit methods.
     */
    public function methods(): JsonResponse
    {
        $customer = auth()->guard('customer')->user();
        $currency = request()->get('currency');

        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'deposit', $currency);

        return response()->json([
            'success' => true,
            'data' => $methods,
        ]);
    }

    /**
     * Get company accounts for deposits.
     */
    public function companyAccounts(): JsonResponse
    {
        $method = request()->get('method');
        $currency = request()->get('currency');

        if (! $method || ! $currency) {
            return response()->json([
                'success' => false,
                'message' => 'Method and currency are required',
            ], 400);
        }

        $companyAccounts = wallet_config('company_accounts', []);
        $accounts = $companyAccounts[$method][$currency] ?? [];

        return response()->json([
            'success' => true,
            'data' => $accounts,
        ]);
    }

    /**
     * Show deposit details.
     */
    public function show(WalletTransaction $transaction): JsonResponse
    {
        $customer = auth()->guard('customer')->user();

        if ($transaction->customer_id !== $customer->id || $transaction->type !== TransactionType::DEPOSIT) {
            return response()->json([
                'success' => false,
                'message' => 'Deposit not found',
            ], 404);
        }

        // Get company accounts for this deposit
        $companyAccounts = wallet_config('company_accounts', []);
        $accounts = $companyAccounts[$transaction->method][$transaction->currency] ?? [];

        return response()->json([
            'success' => true,
            'data' => [
                'transaction' => [
                    'id' => $transaction->id,
                    'reference' => $transaction->reference,
                    'type' => $transaction->type->value,
                    'status' => $transaction->status->value,
                    'method' => $transaction->method,
                    'currency' => $transaction->currency,
                    'amount' => $transaction->amount,
                    'fee' => $transaction->fee,
                    'net_amount' => $transaction->net_amount,
                    'formatted_amount' => $transaction->getFormattedAmount(),
                    'formatted_fee' => $transaction->getFormattedFee(),
                    'formatted_net_amount' => $transaction->getFormattedNetAmount(),
                    'description' => $transaction->description,
                    'metadata' => $transaction->metadata,
                    'external_reference' => $transaction->external_reference,
                    'external_transaction_id' => $transaction->external_transaction_id,
                    'processed_at' => $transaction->processed_at?->toISOString(),
                    'failed_at' => $transaction->failed_at?->toISOString(),
                    'failure_reason' => $transaction->failure_reason,
                    'created_at' => $transaction->created_at->toISOString(),
                    'updated_at' => $transaction->updated_at->toISOString(),
                    'age' => $transaction->getAge(),
                ],
                'company_accounts' => $accounts,
            ],
        ]);
    }
}
