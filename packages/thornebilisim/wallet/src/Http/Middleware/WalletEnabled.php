<?php

namespace Thorne\Wallet\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class WalletEnabled
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (! wallet_config('enabled', true)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => __('wallet::app.errors.wallet_disabled'),
                ], 503);
            }

            abort(503, __('wallet::app.errors.wallet_disabled'));
        }

        return $next($request);
    }
}
