<?php

namespace Thorne\Wallet\Enums;

enum TransactionDirection: string
{
    case INBOUND = 'inbound';
    case OUTBOUND = 'outbound';

    /**
     * Get all transaction directions.
     *
     * @return array
     */
    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get transaction direction label.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::INBOUND => 'Inbound',
            self::OUTBOUND => 'Outbound',
        };
    }

    /**
     * Get transaction direction description.
     *
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::INBOUND => 'Money coming into the account',
            self::OUTBOUND => 'Money going out of the account',
        };
    }

    /**
     * Get the opposite direction.
     *
     * @return self
     */
    public function opposite(): self
    {
        return match ($this) {
            self::INBOUND => self::OUTBOUND,
            self::OUTBOUND => self::INBOUND,
        };
    }

    /**
     * Check if direction is inbound.
     *
     * @return bool
     */
    public function isInbound(): bool
    {
        return $this === self::INBOUND;
    }

    /**
     * Check if direction is outbound.
     *
     * @return bool
     */
    public function isOutbound(): bool
    {
        return $this === self::OUTBOUND;
    }
}
