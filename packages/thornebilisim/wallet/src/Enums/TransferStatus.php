<?php

namespace Thorne\Wallet\Enums;

enum TransferStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case CANCELLED = 'cancelled';

    /**
     * Get all transfer statuses.
     *
     * @return array
     */
    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get transfer status label.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::PROCESSING => 'Processing',
            self::COMPLETED => 'Completed',
            self::FAILED => 'Failed',
            self::CANCELLED => 'Cancelled',
        };
    }

    /**
     * Get transfer status description.
     *
     * @return string
     */
    public function description(): string
    {
        return match ($this) {
            self::PENDING => 'Transfer is waiting to be processed',
            self::PROCESSING => 'Transfer is being processed',
            self::COMPLETED => 'Transfer has been completed successfully',
            self::FAILED => 'Transfer has failed',
            self::CANCELLED => 'Transfer has been cancelled',
        };
    }

    /**
     * Get status color for UI display.
     *
     * @return string
     */
    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::PROCESSING => 'info',
            self::COMPLETED => 'success',
            self::FAILED => 'danger',
            self::CANCELLED => 'secondary',
        };
    }

    /**
     * Check if status is final (cannot be changed).
     *
     * @return bool
     */
    public function isFinal(): bool
    {
        return in_array($this, [self::COMPLETED, self::FAILED, self::CANCELLED]);
    }

    /**
     * Check if status is active (can be processed).
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return in_array($this, [self::PENDING, self::PROCESSING]);
    }

    /**
     * Check if status is successful.
     *
     * @return bool
     */
    public function isSuccessful(): bool
    {
        return $this === self::COMPLETED;
    }

    /**
     * Check if status indicates failure.
     *
     * @return bool
     */
    public function isFailure(): bool
    {
        return in_array($this, [self::FAILED, self::CANCELLED]);
    }
}
