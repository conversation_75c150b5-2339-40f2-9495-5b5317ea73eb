<?php

namespace Thorne\Wallet\Console\Commands;

use Illuminate\Console\Command;
use Thorne\Wallet\Services\LuhnService;

class GenerateAccountNumberCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'wallet:generate-account
                            {currency : Currency code (EUR, USD, MLGR)}
                            {--count=1 : Number of account numbers to generate}
                            {--validate : Validate generated account numbers}';

    /**
     * The console command description.
     */
    protected $description = 'Generate wallet account numbers using Luhn algorithm';

    /**
     * Execute the console command.
     */
    public function handle(LuhnService $luhnService): int
    {
        $currency = strtoupper($this->argument('currency'));
        $count = (int) $this->option('count');
        $validate = $this->option('validate');

        // Check if currency is supported
        if (! wallet_is_supported_currency($currency)) {
            $this->error("Unsupported currency: {$currency}");
            $this->info('Supported currencies: ' . implode(', ', array_keys(wallet_get_enabled_currencies())));
            return self::FAILURE;
        }

        $currencyConfig = wallet_currency_config($currency);
        $isoCode = $currencyConfig['iso_code'];

        $this->info("Generating {$count} account number(s) for {$currency} (ISO: {$isoCode})...");
        $this->newLine();

        $headers = ['Account Number', 'Formatted', 'Currency Code', 'Check Digit', 'Random Part'];
        if ($validate) {
            $headers[] = 'Valid';
        }

        $rows = [];

        for ($i = 0; $i < $count; $i++) {
            try {
                $accountNumber = $luhnService->generateAccountNumber($isoCode);

                $row = [
                    $accountNumber,
                    substr($accountNumber, 0, 3) . '-' . substr($accountNumber, 3, 1) . '-' . substr($accountNumber, 4),
                    $luhnService->extractCurrencyCode($accountNumber),
                    $luhnService->extractCheckDigit($accountNumber),
                    $luhnService->extractRandomPart($accountNumber),
                ];

                if ($validate) {
                    $isValid = $luhnService->validateAccountNumber($accountNumber);
                    $row[] = $isValid ? '✓' : '✗';
                }

                $rows[] = $row;

            } catch (\Exception $e) {
                $this->error("Failed to generate account number: {$e->getMessage()}");
                return self::FAILURE;
            }
        }

        $this->table($headers, $rows);

        if ($validate) {
            $validCount = collect($rows)->where(array_key_last($headers), '✓')->count();
            $this->newLine();
            $this->info("Validation: {$validCount}/{$count} account numbers are valid");
        }

        return self::SUCCESS;
    }
}
